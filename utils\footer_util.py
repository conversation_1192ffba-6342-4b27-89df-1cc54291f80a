from src.app.utils.document_model import DocumentModel
from src.app.services.ocr_service import OCRService
from src.app.utils import constants, models_utils
import re


def extract_footer_data(image_path):
    """Extract and process footer data from the image"""

    # Initialize OCR service and document model
    ocr_service = OCRService()
    document_model = DocumentModel()

    # Get OCR results without row threshold
    ocr_result = ocr_service.extract_text(image_path, use_row_threshold=False)

    # Extract all numbers matching the specified patterns
    numbers = []

    for line in ocr_result.raw_lines:
        # Pattern to match numbers with various formats
        pattern = r'\b\d+[.,]?\s*\d*\b|\b\d{1,3}(?:[.,]\d{3})*(?:[.,]\d+)?\b'
        matches = re.finditer(pattern, line)

        for match in matches:
            num_str = match.group()
            logging.info(f"\nOriginal number found: {num_str}")

            # Clean and standardize the number format
            # Replace ', ' with '.'
            num_str = num_str.replace(', ', '.')
            # Replace '. ' with '.'
            num_str = num_str.replace('. ', '.')
            # Replace remaining ',' with '.'
            num_str = num_str.replace(',', '.')
            # Remove spaces
            num_str = num_str.replace(' ', '')

            logging.info(f"After cleaning: {num_str}")

            # Only process numbers with more than 4 characters
            if len(num_str) > 4:
                try:
                    number = float(num_str)
                    numbers.append(number)
                    logging.info(f"Converted to number: {number}")
                except ValueError as e:
                    logging.info(f"Conversion failed: {e}")
                    continue

    # Remove duplicates and sort numbers from smallest to largest
    numbers = sorted(list(set(numbers)))
    logging.info("\nAll extracted and sorted numbers (without duplicates):")
    logging.info(numbers)

    # Assign values to the document model
    if len(numbers) >= 1:
        # Smallest number is total_pph/total_ttc
        document_model.footer.total_pph = f"{numbers[0]:.2f}"
        document_model.footer.total_ttc = f"{numbers[0]:.2f}"

        if len(numbers) >= 2:
            # Second smallest number is total_ppv
            document_model.footer.total_ppv = f"{numbers[1]:.2f}"

    return document_model


""" -- ******* ---------------------------------------- Testing  --------------------------------------- ******* -- """

if __name__ == "__main__":
    logging.info("Starting OCR processing script...")

    model_image_pairs = [
        # (constants.GPM, "temp_util/BL_footer_cropped_000_GPM__.JPG"),
        # (constants.SOPHADIMS, "temp_util/BL_footer_cropped_000_SOPHADIMS__.JPG"),
        (constants.SOPHADIMS, "temp_util/BL_footer_cropped_001_SOPHADIMS__.JPG"),
        # (constants.COOPER_PHARMA_CASA, "temp_util/BL_footer_cropped_000_COOPER__.JPG"),
        # (constants.SOPHACA, "temp_util/BL_footer_cropped_000_SOPHACA__.JPG"),
    ]

    # Add error handling for file existence
    import os

    if not os.path.exists(model_image_pairs[0][1]):
        logging.info(f"Error: Image file not found at {model_image_pairs[0][1]}")
    else:
        logging.info(f"Processing image: {model_image_pairs[0][1]}")
        result = extract_footer_data(model_image_pairs[0][1])
        logging.info("\nFinal Results:")
        logging.info(f"Total PPH: {result.footer.total_pph}")
        logging.info(f"Total TTC: {result.footer.total_ttc}")
        logging.info(f"Total PPV: {result.footer.total_ppv}")
