# src\app\utils\expected_results_unitTest.py

import src.app.utils.constants as constants
from pathlib import Path

# Get the project root directory
PROJECT_ROOT = Path(__file__).resolve().parent.parent.parent.parent
DATA_DIR = PROJECT_ROOT / "data" / "origin_BL"


EXPECTED_RESULTS = {
    (constants.GPM, str(DATA_DIR / "BLS_1.jpg")): {
        "expected_header_supplier": "GPM",
        "expected_table_length": 4,
        "expected_rows": [
            {"designation": "REPADINA 100VUL OV 95", "quantity": "2"},
            {"designation": "APIXOL SPRAY 30ML AE 89.5", "quantity": "3"},
            {"designation": "MAXICLAV 1G/125MG 24SACH SA", "quantity": "4"},
            {"designation": "ANXIOL BMG 30CP CO", "quantity": "2"},
        ],
    },
    (constants.SOPHADIMS, str(DATA_DIR / "BLS_3.jpg")): {
        "expected_header_supplier": "SOPHADIMS",
        "expected_table_length": 3,
        "expected_rows": [
            {"designation": "BIOMARTIAL 30 GELULE 70", "quantity": "6"},
            {"designation": "ACFOL 5MG BT 280P 11", "quantity": "10"},
            {"designation": "BIOMARTIAL PLUS 30CP 70", "quantity": "2"},
        ],
    },
    (constants.COOPER_PHARMA_CASA, str(DATA_DIR / "BLS_4.jpg")): {
        "expected_header_supplier": "COOPER_PHARMA_CASA",
        "expected_table_length": 5,
        "expected_rows": [
            {"designation": "SUPPOSEDAL PARA NOUR.B12", "quantity": "10"},
            {"designation": "QUINUX", "quantity": "1"},
            {"designation": "GLUCOSE 5% 500ML LAP. \"POCHE\"", "quantity": "9"},
            {"designation": "GLUCOSE 5% 500ML LAP. \"POCHE\"", "quantity": "11"},
            {"designation": "BETADINE BLEU PH2", "quantity": "10"},
        ],
    },
    (constants.SPR, str(DATA_DIR / "BLS_10.jpg")): {
        "expected_header_supplier": "SPR",
        "expected_table_length": 18,
        "expected_rows": [
            {"designation": "CARBOFLORE BTI 30G1135", "quantity": "14"},
            {"designation": "CARBOFLORE BTE/ 3001135", "quantity": "72"},
            {"designation": "DIGESTAL BTI 30CP5", "quantity": "96"},
            {"designation": "DIGESTAL BTE/ 300P5", "quantity": "19"},
            {"designation": "FORTIVISION BTI 300G", "quantity": "12"},
            {"designation": "MENOPHYT BTE/ 300P5", "quantity": "48"},
            {"designation": "MENOPHYT BTI 30CP5", "quantity": "9"},
            {"designation": "SHEN-QI BOTE 10 AMP BUV 10ML", "quantity": "120"},
            {"designation": "SHEN-QI BOTE 10 AMP BUV 10ML", "quantity": "0"},
            {"designation": "SUPERFORM 10 CP ORANGE", "quantity": "120"},
            {"designation": "SUPERFORM 20CP ORANGE", "quantity": "480"},
            {"designation": "SUPERFORM 20 CP ORANGE", "quantity": "96"},
            {"designation": "VITAMAG GM", "quantity": "360"},
            {"designation": "VITAMAG GM IC", "quantity": "72"},
            {"designation": "VITAM4G M4G/00MG57K5", "quantity": "48"},
            {"designation": "VITAM4G M4G/300MG2 STIKS", "quantity": "8"},
            {"designation": "VITAMAG PM", "quantity": "120"},
            {"designation": "VITAMAG PM", "quantity": "24"}
        ],
    },
    (constants.SOPHACA, str(DATA_DIR / "BL_scanned_1_1_cs.jpg")): {
        "expected_header_supplier": "SOPHACA",
        "expected_table_length": 14,
        "expected_rows": [
            {"designation": "TONIFER SIROP 1 50ML.", "quantity": "1"},
            {"designation": "PREDENT GEL. 7/15ML", "quantity": "1"},
            {"designation": "CATAFLAM 50MG BT3/10 CP 1477 doo 1 54 5 0", "quantity": "0"},
            {"designation": "ANAPRED 20MG BT3/20 CPS", "quantity": "1"},
            {"designation": "NEOFORTAN 1060MG BT3/10 CP EF", "quantity": "1"},
            {"designation": "CARDIX 6025 MG BTE/ 28 CP", "quantity": "1"},
            {"designation": "LEVOPHTA 0,057 COLLYRE 4429 doo 1 54 9 O", "quantity": "0"},
            {"designation": "CETIRAL 10MG BT3/15 CP $", "quantity": "1"},
            {"designation": "PURCARB BT3/30 GELULES", "quantity": "1"},
            {"designation": "MEDROL 80 MG BT3/1 APP - GPL a 2489 0oo 1 54 10 0", "quantity": "0"},
            {"designation": "WGEL LAMES WGEL OPHTA", "quantity": "0"},
            {"designation": "XERIUM 20 MG BTE/ 56 CPS $", "quantity": "1"},
            {"designation": "XERIUM 20 MG BTE/ 28 CPS 7606 doo 1 54 3 0", "quantity": "0"},
            {"designation": "ECK.E. DUAL 10ML 9450 Qoo 3 28 8 0", "quantity": "0"}
        ],
    },
    (constants.GPM, str(DATA_DIR / "origin_BL/cropped_GPM_25122024.jpg")): {
        "expected_header_supplier": "GPM",
        "expected_table_length": 15,
        "expected_rows": [
            {"designation": "PULMOFLUIDE SIR AD 150ML SI", "quantity": "0"},
            {"designation": "OSMOSINE 260ML SIR SI", "quantity": "2"},
            {"designation": "AUGMENTIN 1G 24SACH SA", "quantity": "0"},
            {"designation": "DEXERYL CRE 250G CR", "quantity": "1"},
            {"designation": "ICOMB COL 5ML CL", "quantity": "1"},
            {"designation": "TARDYFERON 80MG 30CP CO", "quantity": "2"},
            {"designation": "SPASFON 30CP CO", "quantity": "2"},
            {"designation": "AMLOR 5MG 28CP GM CO", "quantity": "1"},
            {"designation": "VOLTARENE 50MG 30CP CO", "quantity": "1"},
            {"designation": "POLYNORM 2MG 30CP CO", "quantity": "1"},
            {"designation": "CLOMITER CRE CR", "quantity": "0"},
            {"designation": "BETNEVAL - P.DER PD", "quantity": "1"},
            {"designation": "BRUFEN 400MG 30CP CO", "quantity": "0"},
            {"designation": "OREX 30G310 GE", "quantity": "1"},
            {"designation": "TORTIE 4G P348,245 001 H.10825 H7EBB:26", "quantity": "000"}
        ],
    },
    (constants.SOPHADIMS, str(DATA_DIR / "origin_BL/cropped_SOPHADIM_25122024.jpg")): {
        "expected_header_supplier": "SOPHADIMS",
        "expected_table_length": 6,
        "expected_rows": [
            {"designation": "DOLAMINE COMPRESS GM 1", "quantity": "10"},
            {"designation": "NORMOGASTRYL 20 CP EFF 1", "quantity": "3"},
            {"designation": "FARMODOXI 200 MG BTE 10 CPS 1", "quantity": "1"},
            {"designation": "ESPERAL 100 MG 60 CPS 1", "quantity": "2"},
            {"designation": "VOLTARENE 100 MG 5 SUPP 1", "quantity": "1"},
            {"designation": "LYSANXIA OOUTTES.L. 20ME - 1", "quantity": "1"},
        ],
    },
    (constants.COOPER_PHARMA_CASA, str(DATA_DIR / "origin_BL/cropped_COOPER_25122024.jpg")): {
        "expected_header_supplier": "COOPER_PHARMA_CASA",
        "expected_table_length": 3,
        "expected_rows": [
            {"designation": "MORIDIL CREME", "quantity": "3"},
            {"designation": "GLYCAN", "quantity": "1"},
            {"designation": "JARDIANCE 10B30", "quantity": "2"},
        ],
    },
}