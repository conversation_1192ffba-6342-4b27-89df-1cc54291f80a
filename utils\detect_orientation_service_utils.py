from doctr.models._utils import estimate_orientation
import cv2
import os
import logging
import matplotlib.pyplot as plt
import numpy as np

def correct_image_orientation(image_path: str, show_result: bool = True) -> float:
    """
    Detect orientation of the image by analyzing contours (via estimate_orientation),
    correct it, and optionally display the result.
    Returns the detected angle in degrees (negative => rotate by +angle to correct).
    """
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image not found: {image_path}")

    # Load the original image with OpenCV for orientation detection
    orig_img = cv2.imread(image_path, cv2.IMREAD_COLOR)
    if orig_img is None:
        raise ValueError(f"Could not load image with OpenCV: {image_path}")

    # Use docTR's estimate_orientation to get the dominant page angle
    # +angle => page is rotated CCW by `angle` degrees
    # -angle => page is rotated CW by `abs(angle)` degrees
    angle = estimate_orientation(orig_img, n_ct=50, ratio_threshold_for_lines=5)
    logging.info(f"[INFO] Detected angle (counter-clockwise): {angle:.2f} degrees")

    # Rotate the image in the opposite direction to correct it
    rotated_img = _rotate_image(orig_img, angle)

    # -- Display result if requested
    if show_result:
        _show_image("Corrected Image", rotated_img)

    return angle

def _show_image(title: str, image: np.ndarray):
    """
    Show an image using matplotlib (converted from BGR -> RGB).
    """
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    plt.figure(figsize=(8, 6))
    plt.title(title)
    plt.imshow(image_rgb)
    plt.axis('off')
    plt.show()

def _rotate_image(image: np.ndarray, angle: float) -> np.ndarray:
    """
    Rotate an image around its center by the specified angle.
    Here, angle>0 means the image is CCW by that angle.
    To correct it, we rotate by -angle.
    """
    if abs(angle) < 0.5:
        # If angle is negligible, skip to save computation
        return image

    (h, w) = image.shape[:2]
    center = (w // 2, h // 2)

    # We rotate by -angle to "undo" the detected CCW angle
    M = cv2.getRotationMatrix2D(center, -angle, 1.0)
    rotated = cv2.warpAffine(
        image,
        M,
        (w, h),
        flags=cv2.INTER_CUBIC,
        borderMode=cv2.BORDER_REPLICATE
    )
    return rotated

# Usage example:
def main():
    image_path = r"E:\ocr_document_grossiste\temp\origin_images_output\image_origin_a80bc7b5.jpg"
    try:
        angle_detected = correct_image_orientation(image_path, show_result=True)
        logging.info(f"Orientation angle detected (CCW): {angle_detected:.2f} degrees")
        print(f"Orientation angle detected (CCW): {angle_detected:.2f} degrees")
    except Exception as e:
        logging.info(f"Orientation correction error: {e}")
        print(f"Orientation correction error: {e}")

    logging.info("\n" + "=" * 50 + "\n")

if __name__ == "__main__":
    main()