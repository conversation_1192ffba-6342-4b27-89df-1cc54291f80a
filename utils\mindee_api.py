# import requests
#
# url = "POST https://api.mindee.net/v1/products/mindee/invoices/v4/predict"
#
# with open("../data/new_bl.jpg", "rb") as bl_doc:
#     files = {"document": bl_doc}
#     headers = {"Authorization": "Token 8565c99dedd5b0520a09b8905404ee73"}
#     response = requests.post(url, files=files, headers=headers)
#     logging.info(response.text)






from mindee import Client, PredictResponse, product
import json
import logging

# Init a new client
mindee_client = Client(api_key="8565c99dedd5b0520a09b8905404ee73")

# Add your custom endpoint (document)
my_endpoint = mindee_client.create_endpoint(
    account_name="my-account",
    endpoint_name="my-endpoint",
)


# Load a file from disk
input_doc = mindee_client.source_from_path("temp_util/origin_images/SOPHADIM_25122024.jpg")
# input_doc = mindee_client.source_from_path("temp_util/origin_images/image_origin_a2866904_tournee.jpg")
# input_doc = mindee_client.source_from_path("temp_util/BL_table_cropped_000_GPM__.jpg")


# Parse the file.
# The endpoint must be specified since it cannot be determined from the class.
result = mindee_client.parse(
    product.FinancialDocumentV1,
    input_doc,
    # endpoint=my_endpoint
)


# Print a brief summary of the parsed data
# logging.info(result.document)
logging.info(result.document.inference.prediction)
for line_items_elem in result.document.inference.prediction.line_items:
    logging.info(line_items_elem)


logging.info('--------------------------------------------------')
logging.info('--------------------------------------------------')
logging.info('--------------------------------------------------')




for line_items_elem in result.document.inference.prediction.line_items:
    logging.info(line_items_elem.description)
