import traceback
from fastapi.responses import JSONResponse
from fastapi import APIRouter, Depends
import logging
from ..utils.document_model import DocumentModel
from ..utils.models import JSONDataRequest
from src.app.dependencies import get_current_user, check_headers

import httpx


router = APIRouter()

logger = logging.getLogger(__name__)


def Export_Format_WinPlus(documentModel: DocumentModel):  # to Edit
    try:
        # Get data of table
        document = documentModel
        data_tap = []

        for row in document.table:
            # Generate the new JSON structure for TNP
            TNP = {
                "ID": "",
                "categorie": "",
                "forme_galenique": "",
                "designation": row.designation,
                "barcode": "",
                "barcode_2": "",
                "prix_vente": row.ppv if row.ppv else "",
            }
            data_tap.append(TNP)

        return data_tap

    except (ValueError, TypeError) as e:
        logging.error(f"Error in Export_Format_TAP: {traceback.format_exc()}")
        return None


@router.post("/", dependencies=[Depends(get_current_user), Depends(check_headers)])
async def send_json_data(request: JSONDataRequest):
    # Logic to send JSON data to WinPlus
    data = request.data

    # Example: Replace with actual logic to send data to WinPlus
    response = {"status": "success", "message": "Data sent to WinPlus"}

    return JSONResponse(content=response)
