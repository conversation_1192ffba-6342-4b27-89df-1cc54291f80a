from fastapi import APIRouter, File, UploadFile, Form, HTTPException, WebSocketDisconnect, Depends
from src.app.dependencies import get_current_user, check_headers
from fastapi.responses import FileResponse
from src.app.camscanner_lite import magic_pro_filter
import uuid
import traceback
import asyncio
import logging
import sys
from ..services.websocket_manager import manager
from ..utils.helpers import get_temp_path
from ..utils.models import ModelName
from concurrent.futures import ThreadPoolExecutor

router = APIRouter()

logger = logging.getLogger(__name__)

# Initialize a ThreadPoolExecutor
executor = ThreadPoolExecutor(max_workers=10)

@router.post("/")
async def apply_magic_pro_filter_endpoint(image: UploadFile = File(...), model_name: str = Form(...),
                                          random_id: int = None, job_id: str = Form(...)):
    try:

        # Log the entire request in one line
        logging.info(f"Received request from apply_magic_pro_filter_endpoint")
        logging.info(f"image: {image}")
        logging.info(f"model_name: {model_name}")
        logging.info(f"random_id: {random_id}")
        logging.info(f"job_id: {job_id}")


        # Run the `process_magic_pro_filter` function in ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        output_path = await loop.run_in_executor(
            executor,
            lambda: asyncio.run(apply_magic_pro_filter_process(image, model_name, random_id, job_id))
        )

        # Return the processed image as a file download
        return FileResponse(path=output_path, filename=output_path.split('/')[-1])

    except FileNotFoundError as fnf_error:
        logger.error(f"Erreur de fichier: {fnf_error}")
        raise HTTPException(status_code=404, detail="Le fichier image n'a pas été trouvé.")
    except ValueError as val_error:
        logger.error(f"Erreur de valeur: {val_error}")
        raise HTTPException(status_code=400, detail="Valeur incorrecte fournie.")
    except Exception as e:
        logger.error(f"Erreur inattendue: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")


async def apply_magic_pro_filter_process(image: UploadFile = File(...), model_name: ModelName = Form(...),
                                          random_id: int = None, job_id: str = Form(...)):
    try:
        # Generate a random UUID for the image filename
        if not random_id:
            random_id = str(uuid.uuid4())[:8]  # Extract the first 8 characters of the UUID

        # Save the uploaded file to a temporary directory
        output_dir_path = get_temp_path() / "magic_pro_filter_output"
        output_dir_path.mkdir(exist_ok=True)

        # Save the uploaded image to the temporary directory
        image_name = image.filename.split('.')[0] + '_origin_' + random_id + '.jpg'
        image_path = output_dir_path / f"{image_name}"
        with open(image_path, "wb") as buffer:
            buffer.write(await image.read())

        # Output path where the processed image will be saved
        image_name_output = image.filename.split('.')[0] + '_Filtered_' + random_id + '.jpg'
        output_path = output_dir_path / f"{image_name_output}"

        # Apply the magic pro filter
        magic_pro_filter.apply_magic_pro_filter(str(image_path), str(output_path), model_name.upper(), 'custom')

        # Notify the client about progress (100% complete)
        try:
            await manager.send_progress(job_id, 100)
            await asyncio.sleep(0.01)
        except (WebSocketDisconnect, RuntimeError) as e:
            logger.error(f"Error sending progress update: {e}")

        # Return the processed file path
        return str(output_path)

    except FileNotFoundError as fnf_error:
        logger.error(f"Erreur de fichier: {fnf_error}")
        raise HTTPException(status_code=404, detail="Le fichier image n'a pas été trouvé.")
    except ValueError as val_error:
        logger.error(f"Erreur de valeur: {val_error}")
        raise HTTPException(status_code=400, detail="Valeur incorrecte fournie.")
    except Exception as e:
        logger.error(f"Erreur inattendue: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")

