from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
import traceback
import asyncio
import logging
from concurrent.futures import ThreadPoolExecutor
from src.app.utils import constants

router = APIRouter()

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Initialize a ThreadPoolExecutor
executor = ThreadPoolExecutor(max_workers=10)

@router.get("")
@router.get("/")
async def get_suppliers_endpoint():
    try:
        # Run `process_smart_crop` in ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            lambda: asyncio.run(get_suppliers_process())
        )

        # Return the result
        return JSONResponse(content=result)

    except HTTPException as http_exc:
        logger.error(f"Erreur HTTP: {http_exc.detail}")
        return JSONResponse(status_code=http_exc.status_code, content={"message": http_exc.detail})
    except Exception as e:
        logger.error(f"Erreur inattendue: {traceback.format_exc()}")
        return JSONResponse(status_code=500, content={"message": "Erreur interne du serveur"})


async def get_suppliers_process():
    try:
        suppliers = [{"value": supplier, "label": supplier} for supplier in constants.list_models]
        return suppliers

    except HTTPException as http_exc:
        logger.error(f"Erreur HTTP: {http_exc.detail}")
        return JSONResponse(status_code=http_exc.status_code, content={"message": http_exc.detail})
    except Exception as e:
        logger.error(f"Erreur inattendue: {traceback.format_exc()}")
        return JSONResponse(status_code=500, content={"message": "Erreur interne du serveur"})

