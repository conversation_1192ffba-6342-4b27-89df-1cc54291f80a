# from PIL import Image, ImageEnhance
# import cv2
# import numpy as np
# from skimage import exposure
# from skimage.feature import canny
#
#
# def check_resolution(image_path, min_dpi=300):
#     image = Image.open(image_path)
#     dpi = image.info.get('dpi', (72, 72))
#     if dpi[0] < min_dpi or dpi[1] < min_dpi:
#         return False, f"Résolution insuffisante : {dpi} DPI, minimum requis : {min_dpi} DPI"
#     return True, "Résolution adéquate"
#
#
# def check_contrast(image_path, threshold=10):
#     image = Image.open(image_path).convert('L')
#     np_image = np.array(image)
#     contrast = np.std(np_image)
#     if contrast < threshold:
#         return False, f"Contraste insuffisant, écart type : {contrast}, seuil requis : {threshold}"
#     return True, "Contraste adéquat"
#
#
# def check_lighting(image_path, threshold=0.5):
#     image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
#     mean, stddev = cv2.meanStdDev(image)
#     if stddev[0][0] < threshold * mean[0][0]:
#         return False, f"Éclairage non uniforme, écart type : {stddev[0][0]}, seuil requis : {threshold * mean[0][0]}"
#     return True, "Éclairage uniforme"
#
#
# def clean_image(image_path):
#     image = cv2.imread(image_path)
#     cleaned_image = cv2.fastNlMeansDenoisingColored(image, None, 30, 30, 7, 21)
#     temp_path = 'cleaned_image.png'
#     cv2.imwrite(temp_path, cleaned_image)
#     return temp_path
#
#
# def check_orientation(image_path):
#     image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
#     edges = canny(image)
#     lines = cv2.HoughLines(edges, 1, np.pi / 180, 200)
#     if lines is not None:
#         angles = [theta for _, theta in lines[:, 0]]
#         avg_angle = np.mean(angles)
#         if np.abs(np.degrees(avg_angle)) > 5:
#             return False, "Orientation incorrecte"
#     return True, "Orientation correcte"
#
#
# def analyze_image_quality(image_path):
#     report = {}
#
#     resolution_status, resolution_msg = check_resolution(image_path)
#     report['resolution'] = resolution_msg
#
#     contrast_status, contrast_msg = check_contrast(image_path)
#     report['contrast'] = contrast_msg
#
#     lighting_status, lighting_msg = check_lighting(image_path)
#     report['lighting'] = lighting_msg
#
#     orientation_status, orientation_msg = check_orientation(image_path)
#     report['orientation'] = orientation_msg
#
#     return report
#
#
# def main(image_path):
#     report = analyze_image_quality(image_path)
#
#     for criterion, message in report.items():
#         logging.info(f"{criterion.capitalize()} : {message}")
#
#
# # Usage
# image_path = 'votre_image.png'
# main(image_path)