version: 1
disable_existing_loggers: false

formatters:
  default:
    format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
  access:
    format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"

handlers:
  default_file:
    class: logging.handlers.RotatingFileHandler
    filename: log_app_ocr_grossiste_documents.log
    maxBytes: 10485760  # 10 MB
    backupCount: 5
    encoding: utf-8
    formatter: default

  default_console:
    class: logging.StreamHandler
    stream: ext://sys.stdout
    formatter: default

loggers:
  # Root logger
  "":
    level: INFO
    handlers: [default_file, default_console]

  # Your application logger
  "src.api":
    level: INFO
    handlers: [default_file, default_console]
    propagate: false

  # Uvicorn loggers
  "uvicorn":
    level: INFO
    handlers: [default_file, default_console]
    propagate: false

  "uvicorn.error":
    level: INFO
    handlers: [default_file, default_console]
    propagate: false

  "uvicorn.access":
    level: INFO
    handlers: [default_file, default_console]
    propagate: false

  # Optionally capture httpx logs
  "httpx":
    level: INFO
    handlers: [default_file, default_console]
    propagate: false

  # Warnings
  "py.warnings":
    level: WARNING
    handlers: [default_file, default_console]
    propagate: false
