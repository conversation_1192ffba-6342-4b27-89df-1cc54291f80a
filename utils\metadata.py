from pytesseract import image_to_data
import logging
import traceback
from PIL import Image
import easyocr


def process_image(image_path, confidence_threshold=50):
    """Process an image and extract meaningful metadata."""
    reader = easyocr.Reader(['en'])  # Initialize the OCR reader
    results = reader.readtext(image_path, detail=1)

    extracted_data = []
    for result in results:
        text, bbox, confidence = result[1], result[0], result[2] * 100
        if confidence >= confidence_threshold:
            extracted_data.append({
                'text': text.strip(),
                'bbox': bbox,
                'confidence': confidence
            })

    # Example: Group tokens with post-processing (simple heuristic for table rows)
    grouped_data = group_by_rows(extracted_data)

    return grouped_data


def group_by_rows(extracted_data, row_threshold=10):
    """Group extracted text tokens by their vertical position (y-coordinate)."""
    rows = {}
    for token in extracted_data:
        bbox = token['bbox']
        x_min, y_min = bbox[0]  # Top-left corner
        x_max, y_max = bbox[1]  # Bottom-right corner
        row_key = int(y_min // row_threshold)  # Group by row using y_min
        rows.setdefault(row_key, []).append(token)
    return rows


if __name__ == "__main__":
    image_path = "metadata_temp_images/gpm_table.jpg"
    try:
        extracted_metadata = process_image(image_path)
        for row, tokens in extracted_metadata.items():
            logging.info(f"Row {row}:")
            for token in tokens:
                logging.info(f"  Text: {token['text']}, BBox: {token['bbox']}, Confidence: {token['confidence']}")
    except Exception as e:
        logging.error(f"An error occurred: {traceback.format_exc()}")