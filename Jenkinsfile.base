pipeline {
    agent any
    parameters {
        string(name: 'VERSION', defaultValue: '1', description: 'Version for the new Docker image')
    }
    environment {
        IMAGE_NAME = 'ocr_base'
    }
    stages {
        stage('Clean Up Old Images') {
            steps {
                script {
                    // Remove images that start with 'winproduits-delta:V'
                    sh """
                        docker images -q '${IMAGE_NAME}:v*' | xargs -r docker rmi -f
                    """
                }
            }
        }
        stage('Build New Image') {
            steps {
                script {
                    def version = params.VERSION
                    // Build the new Docker image
                    sh "docker build -t ${IMAGE_NAME}:v${version} -f Dockerfile.base ."
                }
            }
        }
    }
}