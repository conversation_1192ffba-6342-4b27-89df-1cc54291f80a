# import numpy as np
# import matplotlib.pyplot as plt
# from skimage.io import imread
# from skimage import transform
# import os
#
# # Load your image
# image_path = 'cropped.jpg'  # Update this path to the correct image file path
#
# if not os.path.exists(image_path):
#     raise FileNotFoundError(f"File not found: {image_path}")
#
# image = imread(image_path)
#
# # Coordinates of the points in the original image
# points_of_interest = np.array([
#     [226.6775480206315, 45.244828003875966],
#     [1370.1429536094959, 49.73878189599483],
#     [1509.6489997173771, 1165.4816093346253],
#     [58.42040700197836, 1190.0694282945738]
# ])
#
# # Coordinates of the projected points
# area_of_projection = np.array([
#     [200, 100],
#     [1200, 50],
#     [1200, 1000],
#     [50, 1000]
# ])
#
# def project_planes(image, src, dst):
#     x_src = [val[0] for val in src] + [src[0][0]]
#     y_src = [val[1] for val in src] + [src[0][1]]
#
#     x_dst = [val[0] for val in dst] + [dst[0][0]]
#     y_dst = [val[1] for val in dst] + [dst[0][1]]
#
#     fig, ax = plt.subplots(1, 2, figsize=(13, 6))
#
#     new_image = image.copy()
#     projection = np.zeros_like(new_image)
#
#     ax[0].imshow(new_image)
#     ax[0].plot(x_src, y_src, 'r--')
#     ax[0].set_title('Area of Interest')
#     ax[1].imshow(projection)
#     ax[1].plot(x_dst, y_dst, 'r--')
#     ax[1].set_title('Area of Projection')
#     plt.tight_layout()
#
# def project_transform(image, src, dst):
#     x_dst = [val[0] for val in dst] + [dst[0][0]]
#     y_dst = [val[1] for val in dst] + [dst[0][1]]
#
#     tform = transform.estimate_transform('projective', np.array(src), np.array(dst))
#     transformed = transform.warp(image, tform.inverse, mode='constant', cval=1)
#
#     plt.figure(figsize=(6, 6))
#     plt.imshow(transformed)
#     plt.plot(x_dst, y_dst, 'r--')
#     plt.tight_layout()
#
# # Display the areas of interest and projection
# project_planes(image, points_of_interest, area_of_projection)
#
# # Apply the projective transformation
# project_transform(image, points_of_interest, area_of_projection)
#
# plt.show()
