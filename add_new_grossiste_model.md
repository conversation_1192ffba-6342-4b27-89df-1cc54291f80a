## Adding a New Grossiest Model

To integrate a new "Grossiest Model" into the application, follow the steps below. This guide outlines the required and non-required code modifications across different files.

### **1. Required Changes**


1. **`src/app/ocr/table.py`**
   - Modify the `OCR_All_TABLE` function: Add the new model structure data to the list.

2. **`src/app/utils/models.py`**
   - Add the new model name to the list in the `ModelName` class.

3. **`src/app/utils/models_utils.py`**
   - Define the structure of the new model in the list in the function `correct_chiffres_calculated`.

4. **`src/app/utils/constants.py`**
   - Include the new model name in the following areas:
     - `Define Name Module`
     - `Define Supplier IDs`
     - `model_config_footer` object
     - `supplier_patterns` object
     - `CORRECT_ADDITIONAL_BL` object
     - `list_models_prices_without_coma` object
     - `list_models` object
     - `additional_table_client` object
     - `footer_heights` object

5. **`In Mobile App:`**
   - Add New Model in the select option on process-doc page.



### **2. Non-Required Changes**

1. **`src/app/services/pre_processing.py`**
   - No need to modify the `additional_table_client` function.

2. **`src/app/services/identify_supplier.py`**
   - No changes required for the `module_thresholds`.

3. **`src/app/utils/constants.py`**
   - Do not add the model name to the following:
     - `FIRST_CELLS_NB`
     - `STANDARD_TABLE_SIZE`
     - `model_settings` object
     - `threshold_settings` object
     - `kernel_sizes_thin_font` object
     - `kernel_sizes_thick_font` object
     - `width_height_boxes_perc_footer` object
     - `model_settings_magicpro_filter` object
     

4. **`src/__main__.py`** and **`src/app/utils/helpers.py`**
   - No changes required in these files.

### **Summary**

Make sure to carefully implement the **required changes** to ensure the new model is fully integrated into the system. Avoid modifying the areas marked as **non-required** to prevent unnecessary complications.

Feel free to refer to the codebase for further details or contact the team for clarification.
