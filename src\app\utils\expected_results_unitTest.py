# src\app\utils\expected_results_unitTest.py

import src.app.utils.constants as constants
from pathlib import Path

# Get the project root directory
PROJECT_ROOT = Path(__file__).resolve().parent.parent.parent.parent
DATA_DIR = PROJECT_ROOT / "data" / "origin_BL"


EXPECTED_RESULTS = {
    (constants.GPM, str(DATA_DIR / "BLS_1.jpg")): {
        "expected_header_supplier": "GPM",
        "expected_table_length": 4,
        "expected_rows": [
            {"designation": "REPADINA 10OVUL", "quantity": "2"},
            {"designation": "APIXOL SPR AD 30ML", "quantity": "3"},
            {"designation": "MAXICLAV 1G/125MG 24SACH", "quantity": "4"},
            {"designation": "ANXIOL 8MG 30CP", "quantity": "2"},
        ],
    },
    (constants.SOPHADIMS, str(DATA_DIR / "BLS_3.jpg")): {
        "expected_header_supplier": "SOPHADIMS",
        "expected_table_length": 3,
        "expected_rows": [
            {"designation": "BIOMARTIAL 30 GELULE", "quantity": "6"},
            {"designation": "ACFOL 5MG BT 28CP", "quantity": "10"},
            {"designation": "BIOMARTIAL PLUS 30CP", "quantity": "2"},
        ],
    },
    (constants.COOPER_PHARMA_CASA, str(DATA_DIR / "BLS_4.jpg")): {
        "expected_header_supplier": "COOPER_PHARMA_CASA",
        "expected_table_length": 5,
        "expected_rows": [
            {"designation": "SUPPOSEDAL PARA NOUR B12", "quantity": "10"},
            {"designation": "QUINUX 10 B1", "quantity": "1"},
            {"designation": "GLUCOSE 5% 500ML LAP \"POCHE\"", "quantity": "9"},
            {"designation": "GLUCOSE 5% 500ML LAP \"POCHE\"", "quantity": "11"},
            {"designation": "BETADINE BLEU PH2", "quantity": "10"}
        ],
    },
    (constants.SPR, str(DATA_DIR / "BLS_10.jpg")): {
        "expected_header_supplier": "SPR",
        "expected_table_length": 18,
        "expected_rows": [
            {"designation": "CARBOFLORE BT/30GLLES", "quantity": "14"},
            {"designation": "CARBOFLORE BT/30GLLES", "quantity": "72"},
            {"designation": "DIGESTAL BT/30CPS", "quantity": "98"},
            {"designation": "DIGESTAL BT/30CPS", "quantity": "19"},
            {"designation": "FORTIVISION BT/30DG", "quantity": "12"},
            {"designation": "MENOPHYT BT/30CPS", "quantity": "48"},
            {"designation": "MENOPHYT BT/30CPS", "quantity": "9"},
            {"designation": "SHEN-QI BTE 10 AMP BUV 10ML", "quantity": "120"},
            {"designation": "SHEN-QI BTE 10 AMP BUV 10ML", "quantity": "0"},
            {"designation": "SUPERFORM 10 CP ORANGE", "quantity": "120"},
            {"designation": "SUPERFORM 20 CP ORANGE", "quantity": "480"},
            {"designation": "SUPERFORM 20 CP ORANGE", "quantity": "96"},
            {"designation": "VITAMAG GM", "quantity": "360"},
            {"designation": "VITAMAG GM", "quantity": "72"},
            {"designation": "VITAMAG MAG 300MG STIKS", "quantity": "48"},
            {"designation": "VITAMAG MAG 300MG STIKS", "quantity": "8"},
            {"designation": "VITAMAG PM", "quantity": "120"},
            {"designation": "VITAMAG PM", "quantity": "24"}
        ],
    },
    (constants.SOPHACA, str(DATA_DIR / "BL_scanned_1_1_cs.jpg")): {
        "expected_header_supplier": "SOPHACA",
        "expected_table_length": 14,
        "expected_rows": [
            {"designation": "MALTOFER SIROP 150ML", "quantity": "1"},
            {"designation": "PREDENT GEL T/15ML", "quantity": "1"},
            {"designation": "CATAFLAM 50MG BT/10 CP", "quantity": "0"},
            {"designation": "ANAPRED 20MG BTE/20 CPS", "quantity": "1"},
            {"designation": "NEOFORTAN 160MG BT/10 CP EF", "quantity": "1"},
            {"designation": "CARDIX 6.25 MG BT/28 CP", "quantity": "1"},
            {"designation": "LEVUPHTA 0.05% COLLYRE", "quantity": "0"},
            {"designation": "CETIRAL 10MG BT/15 CP $", "quantity": "1"},
            {"designation": "PURCARB BTE/30 GELULES", "quantity": "1"},
            {"designation": "DEPO-MEDROL. 80 MG BT/1 AMP", "quantity": "0"},
            {"designation": "GEL LARMES GEL OPHTA 10G", "quantity": "0"},
            {"designation": "XERIUM 20 MG BT/56 CPS $", "quantity": "1"},
            {"designation": "XERIUM 20 MG BT/ 28 CPS", "quantity": "0"},
            {"designation": "EOLE DUAL 10ML", "quantity": "0"}
        ],
    },
    (constants.GPM, str(DATA_DIR / "origin_BL/cropped_GPM_25122024.jpg")): {
        "expected_header_supplier": "GPM",
        "expected_table_length": 14,
        "expected_rows": [
            {"designation": "PULMOFLUIDE SIR AD 150ML SI", "quantity": "0"},
            {"designation": "OSMOSINE 260ML SIR SI", "quantity": "2"},
            {"designation": "AUGMENTIN 1G 24SACH SA", "quantity": "0"},
            {"designation": "DEXERYL CRE 250G CR", "quantity": "1"},
            {"designation": "ICOMB COL 5ML CL", "quantity": "1"},
            {"designation": "TARDYFERON 80MG 30CP CO", "quantity": "2"},
            {"designation": "SPASFON 30CP CO", "quantity": "2"},
            {"designation": "AMLOR 5MG 28CP GM CO", "quantity": "1"},
            {"designation": "VOLTARENE 50MG 30CP CO", "quantity": "1"},
            {"designation": "POLYNORM 2MG 30CP CO", "quantity": "1"},
            {"designation": "CLOMITER CRE CR", "quantity": "0"},
            {"designation": "BETNEVAL P.DER PD", "quantity": "1"},
            {"designation": "BRUFEN 400MG 30CP CO", "quantity": "0"},
            {"designation": "OREXVIT 30GELU GE", "quantity": "1"},
        ],
    },
    (constants.SOPHADIMS, str(DATA_DIR / "origin_BL/cropped_SOPHADIM_25122024.jpg")): {
        "expected_header_supplier": "SOPHADIMS",
        "expected_table_length": 6,
        "expected_rows": [
            {"designation": "DOLAMINE COMPRESS GM", "quantity": "10"},
            {"designation": "NORMOGASTRYL 20 CP EFF", "quantity": "3"},
            {"designation": "FARMODOXI 200 MG BTE 10 CPS", "quantity": "1"},
            {"designation": "ESPERAL 100 MG 60 CPS", "quantity": "2"},
            {"designation": "VOLTARENE 100 MG 5 SUPP", "quantity": "1"},
            {"designation": "LYSANXIA GOUTTES FL 20ML", "quantity": "1"},
        ],
    },
    (constants.COOPER_PHARMA_CASA, str(DATA_DIR / "origin_BL/cropped_COOPER_25122024.jpg")): {
        "expected_header_supplier": "COOPER_PHARMA_CASA",
        "expected_table_length": 3,
        "expected_rows": [
            {"designation": "MORIDIL CREME", "quantity": "3"},
            {"designation": "GLYCAN 850 RET.B60", "quantity": "1"},
            {"designation": "JARDIANCE 10 B30", "quantity": "2"},
        ],
    },
}