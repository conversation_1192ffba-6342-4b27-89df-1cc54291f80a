from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
import re
import json
from src.app.utils import constants
from fuzzywuzzy import process, fuzz
from difflib import SequenceMatcher
from docx.shared import Pt
from docx import Document
from fpdf import FPDF
import os
from pathlib import Path
import requests
import asyncio
from io import BytesIO
from PIL import Image
from concurrent.futures import ThreadPoolExecutor
import zipfile
import fitz  # PyMuPDF
import cv2
import aiofiles
from fastapi import HTTPException
from src.app.utils.constants import SUPPLIER_IDS
import numpy as np
import logging

__all__ = [
    'read_json_and_store_final_result',
    'store_and_save_documentModel_toJson',
    'store_and_save_as_Json',
    'showOutputInConsol_GLOBAL',
    'saveDataIn_PDF_TXT_document_GLOBAL',
    'correct_bt_bte_formats_v2',
    'check_presence',
    'contains_number',
    'correct_number',
    'correct_number_except_coma',
    'normalize_price_float',
    'normalize_price_string_count_after_coma',
    'count_short_parts',
    'clean_chars_ocr_lines_last_parts',
    'is_float',
    'is_integer',
    'is_numeric',
    'select_name_root_from_line',
    'contains_suffixes_from_two_lists',
    'is_suffix_end_part',
    'replace_double_spaces',
    'matches_any_pattern',
    'matches_dosage_or_presentation',
    'is_exact_match_hold_name',
    'is_exact_match_hold_name_with_value',
    'is_exact_match_part_name',
    'remove_selected_special_characters',
    'keyword_in_text',
    'remove_pipe_if_not_merge_with_number',
    'fuzzywazzy_part_with_array',
    'generate_states_Json_Docx',
    '_matrix_generate_states_PDF',
    'fuzzywazzy_part_with_array_parameter',
    'main_matching_data_docx',
    'generate_states_PDF',
    'get_temp_path',
    'get_base_name',
    'load_image_from_url',
    'write_image_async',
    'zip_image_async',
    'pdf_image_async',
    'get_supplier_id',
    'clean_text_from_special_chars',
    'is_valid_word',
    'is_valid_word_2',
]

"""-----************ Post Processing Data - GLOBAL FUNCTIONS & VARIABLES ************ -----"""


def get_supplier_id(supplier_name: str) -> int:
    """
    Retrieve the supplier ID based on the supplier name.
    :param supplier_name: The name of the supplier.
    :return: The supplier ID if the supplier name is found, otherwise raise an exception.
    """
    
    if supplier_name == 'AUTRE':
        supplier_name = 'GLOBAL'
    if supplier_name in SUPPLIER_IDS:
        return SUPPLIER_IDS[supplier_name]
    else:
        raise ValueError(f"Supplier name '{supplier_name}' is not recognized.")


def read_json_and_store_final_result(ocr_header_all, ocr_table_all, model):
    # Convert JSON strings to Python objects
    header_data = json.loads(ocr_header_all)
    table_data = json.loads(ocr_table_all)

    # Merge the Python objects into a single dictionary
    BL_OCR_Result = {
        'header': header_data,
        'table': table_data
    }

    # Create the directory if it doesn't exist
    output_dir = "../../../output_json"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Generate the file path
    file_path = os.path.join(output_dir, f"{model.lower()}_final_result.json")

    # Convert the merged dictionary back to JSON string if necessary
    final_result_ocr = json.dumps(BL_OCR_Result, indent=4, ensure_ascii=False)

    # Store the file JSON if necessary
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(BL_OCR_Result, f, indent=4, ensure_ascii=False)

    return final_result_ocr


def store_and_save_documentModel_toJson(documentModel, model, module='default'):
    # Create the directory if it doesn't exist
    output_dir = "../../../output_json"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Generate the file path
    file_path = os.path.join(output_dir, f"{module.lower()}_{model.lower()}_final_result.json")

    # Convert the merged dictionary back to JSON string if necessary
    final_result_ocr = documentModel.to_json()

    # Store the file JSON if necessary
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(final_result_ocr)  # Write the JSON data to the file

    # logging.info("doc :" , final_result_ocr)

    return final_result_ocr


def store_and_save_as_Json(results, model):
    # Create the directory if it doesn't exist
    output_dir = "../../../output_json"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Generate the file path
    file_path = os.path.join(output_dir, f"{model.lower()}.json")

    # Write the JSON data to the file
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=4, ensure_ascii=False)


def showOutputInConsol_GLOBAL(text):
    # Split the text by line breaks
    text_lines = text.split('\n')

    for line in text_lines:
        logging.info(line)


def saveDataIn_PDF_TXT_document_GLOBAL(text):
    # Split the text by line breaks
    text_lines = text.split('\n')

    # Create a PDF document
    pdf_path = '../output_ocr/BL_table_____.pdf'
    c = canvas.Canvas(pdf_path, pagesize=letter)

    # Define font size and line height
    font_size = 12
    line_height = 1.5 * font_size

    # Iterate over each line to draw bounding boxes and text, and add text to PDF
    y_position = 700  # Starting y-position for text in PDF

    # Track the current page
    current_page = 0

    # Print and write each line to a file with line breaks
    with open('../output_ocr/BL_text.txt', 'w') as file:
        for line in text_lines:
            logging.info(line)
            file.write(line + '\n')

            # Add the text to the PDF document
            c.setFont("Helvetica", font_size)
            c.drawString(50, y_position, line)
            y_position -= line_height

    # Parse each line and draw bounding boxes
    for line in text_lines:
        # Skip empty lines
        if not line.strip():
            continue

        if y_position <= 0:
            # Add a new page
            c.showPage()
            current_page += 1
            y_position = 700

        # Split line into character, left, top, right, bottom
        parts = line.split()
        try:
            character = parts[0]
            left, top, right, bottom = map(int, parts[1:5])
        except ValueError:
            # Skip lines that don't contain valid bounding box information
            continue

    y_position -= line_height

    # Save the PDF document
    c.save()


def correct_bt_bte_formats_v2(line):
    # Replace multiple spaces with a single space and strip leading/trailing spaces
    line = re.sub(r'\s{2,}', ' ', line).strip()

    # Replace known OCR misinterpretations using a dictionary for replacements
    replacements = {
        'BT/': ' BT/', 'BTE/': ' BTE/', 'BT!': ' BT/', 'BTE!': ' BTE/', '&T/': ' BT/', '&TE/': ' BTE/',

        '8T': ' BT', 'RT/': ' BT/', 'PT/': ' BT/', 'ET/': ' BT/', 'BY/': ' BT/', 'BV/': ' BT/',
        'BYE/': ' BTE/', 'BVE/': ' BTE/', 'BT /': ' BT/',  # ' T/': ' BT/',

        'BT\'': ' BT/', 'RT\'': ' BT/', 'PT\'': ' BT/', 'ET\'': ' BT/', 'BY\'': ' BT/', 'BV\'': ' BT/',
        'BYE\'': ' BTE/', 'BVE\'': ' BTE/', 'BT \'': ' BT/',  # ' T\'': ' BT/',

        'BT’': ' BT/', 'RT’': ' BT/', 'PT’': ' BT/', 'ET’': ' BT/', 'BY’': ' BT/', 'BV’': ' BT/',
        'BYE’': ' BTE/', 'BVE’': ' BTE/', 'BT ’': ' BT/',  # ' T’': ' BT/',

        # '8T': ' BT',  'B1': ' BT', 'R1': ' BT', 'P1': ' BT', 'E1': ' BT', 'BY1': ' BT', 'BV1': ' BT',
        'BYE1': ' BTE', 'BVE1': ' BTE', 'BT 1': ' BT',  # ' T1': ' BT',

        'R7/': ' BT/', 'P7/': ' BT/', 'E7/': ' BT/', 'B7/': ' BT/', 'BVE7/': ' BTE/',
        'BTE7/': ' BTE/',  # ' T7/': ' BT/',

        'R7\'': ' BT/', 'P7\'': ' BT/', 'E7\'': ' BT/', 'B7\'': ' BT/', 'BVE7\'': ' BTE/',
        # ' T7\'': ' BT/',   ' T7/': ' BT/',
        'BTE7\'': ' BTE/',

        # 'R7’': ' BT/', 'P7’': ' BT/', 'E7’': ' BT/', 'B7’': ' BT/', 'BVE7’': ' BTE/',
        'BTE7’': ' BTE/'  # ' T7’': ' BT/',
    }

    for key, value in replacements.items():
        line = line.replace(key, value)

    # Replace 'BTx/' with 'BTE/'
    line = re.sub(r'BT\S*/', 'BTE/', line)
    line = re.sub(r'BT\D*/', 'BTE/', line)

    # Remove two spaces
    line = line.replace('  ', ' ')

    # # Correct numbers following "BT/" or "BTE/"
    # line = re.sub(r'(BT/|BTE/)\s?([^\s\d]*\d+[^\s\d]*)', lambda m: m.group(1) + re.sub(r'[^\d]', '', m.group(2)), line)

    return line


# def check_presence(array, line):
#     for value in array:
#         if value in line:
#             return True  # Returns True and the value if found
#     return False  # Returns False and None if no value is found

def check_presence(array, word):
    # Ensure that 'word' is a string before attempting to use string methods
    if not isinstance(word, str):
        # If 'word' is not a string, return False and None
        return False, None
    for value in array:
        if value.lower() in word.lower():
            return True, value  # Returns True and the value if found
    return False, None  # Returns False and None if no value is found


def contains_number(input_string):
    """
    Check if the input string contains any digit.
    """
    return bool(re.search(r'\d', input_string))


def correct_number(input_str):
    """
    Apply corrections from the dictionary to the input string.
    """
    correction_dict = constants.quantity_correction_dict
    corrected_str = "".join(correction_dict.get(char, char) for char in input_str)
    return corrected_str


# Except word that have '.' or ','
def correct_number_except_coma(input_str):
    """
    Apply corrections from the dictionary to the input string.
    """
    correction_dict = constants.quantity_correction_dict

    corrected_str = ""
    exclude_chars = {'.', ','}
    for char in input_str:
        if char in exclude_chars:
            corrected_str += "" + char
        else:
            corrected_str += "".join(correction_dict.get(char, char))

    # corrected_str = "".join(correction_dict.get(char, char) if char not in exclude_chars else char for char in input_str)

    return corrected_str


def normalize_price_float(price):
    if not price:
        return 0.000
    # Replace commas possibly used as thousands separators
    price = price.replace(',', '.')
    try:
        return float(price)
    except ValueError:
        return price


def normalize_price_string_count_after_coma(part, count=None):
    if count:
        price = correct_number_except_coma(part)
    else:
        price = correct_number(part)

    try:
        # Convert the string to a float, replace comma with dot for decimal, and divide by 100
        normalized_price = price.replace(',', '.')

        # Ensure there's a decimal point, add one if missing
        if '.' not in normalized_price and count and len(part) > 2:
            normalized_price = normalized_price[:-count] + '.' + normalized_price[-count:]

        # Format the price to 2 decimal places as a string
        price = normalized_price
    except ValueError:
        price = part

    return price


# is used to count the number of "short" parts (words or segments) in a given list of parts
def count_short_parts(parts, count):
    # Get the last count parts of the line
    last_count_parts = parts[-count:] if len(parts) >= count else parts

    # Count parts with less than three characters
    short_parts_count = sum(1 for part in last_count_parts if len(part) < count)

    return short_parts_count


def clean_chars_ocr_lines_last_parts(lines, count, additional_section_start_index=None):
    # count is the number of words to ignore the special character
    # Example: count = 4 (e.g., '3 NADIRE BT/ 20 CP 100.00 120.00 & L213001 | 20/3' becomes '3 NADIRE BT/ 20 CP 100.00 120.00 L213001 20/3')

    cleaned_lines = []

    # Determine the lines to check based on additional_section_start_index
    if additional_section_start_index is not None:
        lines_to_check = lines[:additional_section_start_index]
        lines_to_append_as_is = lines[additional_section_start_index:]
    else:
        lines_to_check = lines
        lines_to_append_as_is = []

    # Process the lines that need cleaning
    for line in lines_to_check:
        parts = line.split()
        # Determine the starting index for the last 'count' parts
        start_index = max(0, len(parts) - count)  # Ensure it does not go negative

        # Retain all parts up to the last 'count'
        cleaned_parts = parts[:start_index]

        # Check the last 'count' parts and only add them if they have 2 or more characters
        for part in parts[start_index:]:
            if len(part) >= 2:
                cleaned_parts.append(part)

        # Join the cleaned parts back into a single string
        cleaned_line = ' '.join(cleaned_parts)
        cleaned_lines.append(cleaned_line)

    # Append lines that do not require cleaning
    cleaned_lines.extend(lines_to_append_as_is)

    return cleaned_lines


def is_float(s):
    if s is None:
        return False
    try:
        s = s.replace(',', '.')
        float(s)
        return True
    except ValueError:
        return False


def is_integer(s):
    if s is None:
        return False
    try:
        int(s)
        return True
    except ValueError:
        return False


def is_numeric(s):
    if s is None:
        return False
    try:
        s = s.replace('.', '')
        s = s.replace(',', '')
        # s = correct_number(s)
        int(s)
        return True
    except ValueError:
        return False

def is_valid_word_3(word):
    """
    Checks if a word is valid:
    - At least 3 characters long.
    - Contains at least one alphanumeric character.
    - Not purely numeric or special characters.
    """
    # At least 3 characters
    if len(word) < 3:
        return False
    # Contains at least one alphanumeric character
    if not any(c.isalnum() for c in word):
        return False
    # Not purely numeric
    if word.isnumeric():
        return False
    # Word is valid
    return True


def is_valid_word_2(word):
    if len(word) < 3:
        return False
    letters = sum(c.isalpha() for c in word)
    digits = sum(c.isdigit() for c in word)
    # Word is valid if it contains more letters than digits
    if letters <= digits:
        return False
    return True

def is_valid_word(word):
    """
    Checks if a word is valid:
    - At least 3 characters long.
    - Contains at least one alphanumeric character.
    - Not purely numeric.
    """
    if len(word) < 3:
        return False
    # Check if the word contains at least one letter or number
    if not re.search(r"[a-zA-Z0-9]", word):
        return False
    # Check if the word is purely numeric
    if word.isnumeric():
        return False
    return True

def select_name_root_from_line(line):
    """Select the appropriate word from the line based on the given criteria."""
    # Normalize the line by removing extra spaces
    line = ' '.join(line.split())
    words = line.split()

    try:
        # Prioritize the first word if it is valid
        if len(words) > 0 and is_valid_word(words[0]):
            return words[0], 0

        # Iterate through the words to find the root name
        for i, word in enumerate(words):
            # Skip invalid words
            if not is_valid_word(word):
                continue

            # Perform fuzzy matching to identify the root name
            combined_matches = process.extract(
                str(word),
                [str(w) for w in constants.CORRECT_WORDS],
                scorer=fuzz.ratio,
                limit=None
            )

            # Find the best match for this word
            if combined_matches:
                combined_best_match = max(combined_matches, key=lambda x: x[1])
                if combined_best_match[1] >= 80:  # Fuzzy match threshold
                    return word, i

    except ValueError:
        return None, None  # Return None if no suitable word is found

    return None, None  # Return None if no suitable word is found


def contains_suffixes_from_two_lists(s, list1, list2):
    # Check if at least one suffix from list1 is in string s
    found_in_list1 = any(suffix.lower() in s.lower() for suffix in list1)
    # Check if at least one suffix from list2 is in string s
    found_in_list2 = any(suffix.lower() in s.lower() for suffix in list2)

    # Return True if both lists have at least one matching suffix in the string
    return found_in_list1 and found_in_list2


def is_suffix_end_part(word, part):
    # Adjust pattern to detect suffix following alphanumeric characters
    pattern = rf'{re.escape(word)}[\.,\?!]*$'
    return bool(re.search(pattern, part))


def replace_double_spaces(lines):
    # Replace double spaces with a single space in each line
    return [line.replace('  ', ' ') for line in lines]


def matches_any_pattern(word, patterns):
    """ Return True if the word matches any of the compiled regex patterns. """
    return any(pattern.search(word) for pattern in patterns)


def matches_dosage_or_presentation(word, dosage_patterns, presentation_patterns):
    """Check if the word matches any of the dosage or presentation patterns."""
    for pattern in dosage_patterns + presentation_patterns:
        if pattern.search(word):
            return True
    return False


def is_exact_match_hold_name(word, valid_terms):
    """ Check if the word exactly matches any term in the valid_terms list, case-insensitively. """
    for term in valid_terms:
        if term.lower() == word.lower():
            return True

    return False


def is_exact_match_hold_name_with_value(word, valid_terms):
    """ Check if the word exactly matches any term in the valid_terms list, case-insensitively. """
    for term in valid_terms:
        if term.lower() == word.lower():
            return True, term

    return False, None


def is_exact_match_part_name(word, valid_terms):
    for part_string in valid_terms:
        if part_string.lower() in word.lower():
            return True

    return False


def remove_selected_special_characters(line):
    """
    Remove special characters from the line except for specified ones:
    '/', '.', ',', '%', '(', ')', '-', and ':'.
    """
    # Allow only alphanumeric characters, spaces, and the specified characters
    cleaned_line = re.sub(r"[^a-zA-Z0-9\s\/.,%()\-:]", '', line)
    return cleaned_line


# Define a function to check if any keyword from a list exists in a given text string
def keyword_in_text(keyword_list, text):
    """Check if any of the keywords exists in the given text."""
    return any(kw in str(text).upper() for kw in keyword_list)


def remove_pipe_if_not_merge_with_number(input_string):
    words = str(input_string).split()

    for i, word in enumerate(words):
        if '|' in word and len(word) > 1:
            words[i] = word.replace('|', '')

    result = ' '.join(words)
    return result


# Matching with 70%
def fuzzywazzy_part_with_array(part, array):
    combined_matches = process.extract(str(part), [str(w) for w in array],
                                       scorer=fuzz.ratio, limit=None)

    # Check if combined_matches is empty
    if not combined_matches:
        return None

    combined_best_match = max(combined_matches, key=lambda x: x[1])
    if combined_best_match[1] >= 70:
        return combined_best_match
    return None


# Matching with any percentage param
def fuzzywazzy_part_with_array_parameter(part, array, percentage):
    combined_matches = process.extract(str(part), [str(w) for w in array],
                                       scorer=fuzz.ratio, limit=None)

    # Check if combined_matches is empty
    if not combined_matches:
        return None

    combined_best_match = max(combined_matches, key=lambda x: x[1])
    if combined_best_match[1] >= percentage:
        return combined_best_match
    return None


""" ------------------ Generate Docx Statistic for each output text (bafore and after correction) -------------------- """
""" ------------------------------------------------------------------------------------------------------------------ """


def calculate_match_percentage(original, ocr_result):
    chars_to_remove = [',', '.']

    for char in chars_to_remove:
        original = original.replace(char, '')
        ocr_result = ocr_result.replace(char, '')

    # Convert texts to uppercase
    original = original.upper()
    ocr_result = ocr_result.upper()

    matcher = SequenceMatcher(None, original.split(), ocr_result.split())
    match_ratio = matcher.ratio()
    return match_ratio * 100


def add_highlighted_text(paragraph, text, highlight):
    run = paragraph.add_run(text)
    run.font.size = Pt(12)
    if highlight:
        run.font.highlight_color = 3  # Yellow highlight


def read_file_with_multiple_encodings(file_path):
    encodings = ['utf-8', 'latin-1', 'cp1252']
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read().split('\n\n')
        except UnicodeDecodeError:
            continue
    raise UnicodeDecodeError(f"Unable to read the file {file_path} with any of the specified encodings.")


def clean_text_for_comparison(text, chars_to_remove):
    for char in chars_to_remove:
        text = text.replace(char, '')
    return text


# Function to clean text while keeping specified characters
def clean_text_from_special_chars(text):
    return ''.join(c for c in text if c.isalnum() or c.isspace() or c in constants.keep_chars)


def create_comparison_word_document(original_data, ocr_data, comparison_results, output_path):
    chars_to_remove = [',', '.']

    doc = Document()
    for doc_type, match_percentage in comparison_results:
        doc.add_heading(f'Document Type: {doc_type} (Match Percentage: {match_percentage:.2f}%)', level=1)

        # Add Original Text
        doc.add_heading('Original Text', level=2)
        for line in original_data[doc_type].split('\n'):
            doc.add_paragraph(line)

        # Add OCR Result with Highlighted Differences
        doc.add_heading('OCR Result', level=2)
        # ocr_paragraph = doc.add_paragraph()

        # # Use SequenceMatcher to align words more flexibly
        # original_words = original_data[doc_type].split()
        # ocr_words = ocr_data[doc_type].split()

        original_lines = original_data[doc_type].split('\n')
        ocr_lines = ocr_data[doc_type].split('\n')

        for o_line, r_line in zip(original_lines, ocr_lines):
            ocr_paragraph = doc.add_paragraph()

            # Use SequenceMatcher to align words more flexibly
            original_words = o_line.split()
            ocr_words = r_line.split()

            matcher = SequenceMatcher(None, original_words, ocr_words)
            for opcode in matcher.get_opcodes():
                if opcode[0] == 'equal':
                    for word in original_words[opcode[1]:opcode[2]]:
                        add_highlighted_text(ocr_paragraph, word + ' ', False)
                elif opcode[0] == 'replace' or opcode[0] == 'insert':
                    for word in ocr_words[opcode[3]:opcode[4]]:
                        add_highlighted_text(ocr_paragraph, word + ' ', True)
                elif opcode[0] == 'delete':
                    for word in original_words[opcode[1]:opcode[2]]:
                        add_highlighted_text(ocr_paragraph, '[MISSING] ', True)

        doc.add_page_break()

    doc.save(output_path)


def main_matching_data_docx(type):
    # Ensure the 'type' parameter is one of the allowed values
    if type.lower() not in ['after', 'before']:
        raise ValueError("Invalid value for 'type'. Expected 'after' or 'before'.")

    # Define paths
    original_text_path = '../../../original_txt/original_text_real.txt'

    if type.lower() == 'before':
        ocr_texts_dir = '../../../output_before_corr_txt/'

        # List of OCR output files
        ocr_files = [
            'earthMomma_5k_intr_tesseract_ocr.txt',
            'earthmomma_lite_tesseract_ocr.txt',
            'earthMomma1_tesseract_ocr.txt',
            'earthMomma2_18k_tesseract_ocr.txt',
            'ninetyseven1_tesseract_ocr.txt',
            'ninetyseven2_tesseract_ocr.txt',
            'ninetysevenlite_tesseract_ocr.txt',
            'default_tesseract_ocr.txt',
        ]
    elif type.lower() == 'after':
        ocr_texts_dir = '../../../output_after_corr_txt/'

        # List of OCR output files corrected
        ocr_files = [
            'earthMomma_5k_intr_corrected_ocr.txt',
            'earthmomma_lite_corrected_ocr.txt',
            'earthMomma1_corrected_ocr.txt',
            'earthMomma2_18k_corrected_ocr.txt',
            'ninetyseven1_corrected_ocr.txt',
            'ninetyseven2_corrected_ocr.txt',
            'ninetysevenlite_corrected_ocr.txt',
            'default_corrected_ocr.txt',
        ]

    # Load the original text file
    with open(original_text_path, 'r', encoding='utf-8') as f:
        original_text = f.read().split('\n\n')

    documents = ['Processing model: GPM', 'Processing model: SOPHADIMS', 'Processing model: COOPER_PHARMA_CASA',
                 'Processing model: SPR', 'Processing model: SOPHACA']

    # Process each OCR output file
    for ocr_file in ocr_files:
        ocr_file_path = os.path.join(ocr_texts_dir, ocr_file)

        # Load the OCR text file
        # with open(ocr_file_path, 'r', encoding='utf-8') as f:
        #     ocr_text = f.read().split('\n\n')
        ocr_text = read_file_with_multiple_encodings(ocr_file_path)

        original_data = {}
        ocr_data = {}

        for doc in documents:
            original_data[doc] = next((text for text in original_text if doc in text), "")
            ocr_data[doc] = next((text for text in ocr_text if doc in text), "")

        comparison_results = []

        for doc in documents:
            original = original_data[doc]
            ocr_result = ocr_data[doc]
            match_percentage = calculate_match_percentage(original, ocr_result)
            comparison_results.append((doc, match_percentage))

        # Create the Word document with highlighted differences
        output_docx_path = os.path.join(ocr_texts_dir, ocr_file.replace('.txt', '.docx'))
        create_comparison_word_document(original_data, ocr_data, comparison_results, output_docx_path)


""" ------------------------------ Generate Statistic as PDF File from docx corrected -------------------------------- """
""" ------------------------------------------------------------------------------------------------------------------ """


def extract_statistics_from_docx(docx_path):
    statistics = {}
    doc = Document(docx_path)
    model = os.path.basename(docx_path).replace('_tesseract_ocr.docx', '')

    current_doc_type = None

    for paragraph in doc.paragraphs:
        text = paragraph.text.strip()
        if "Processing model:" in text:
            current_doc_type = text.split("Processing model:")[1].split("(")[0].strip()
        if "Match Percentage:" in text:
            percentage = float(text.split("Match Percentage:")[1].split("%")[0].strip())
            if model not in statistics:
                statistics[model] = {}
            statistics[model][current_doc_type] = percentage

    return statistics


def create_pdf(statistics, output_path):
    pdf = FPDF()
    pdf.add_page()
    pdf.set_font("Arial", size=12)

    pdf.cell(200, 10, txt="OCR Model Comparison Statistics", ln=True, align="C")

    best_models = {}

    for model, stats in statistics.items():
        pdf.ln(10)
        pdf.set_font("Arial", style='B', size=12)
        pdf.cell(200, 10, txt=model, ln=True, align="L")

        pdf.set_font("Arial", size=12)
        for doc_type, percentage in stats.items():
            pdf.cell(200, 10, txt=f"--- {doc_type} : {percentage:.2f}%", ln=True, align="L")
            if doc_type not in best_models or best_models[doc_type][1] < percentage:
                best_models[doc_type] = (model, percentage)

    pdf.ln(10)
    pdf.set_font("Arial", style='B', size=12)
    pdf.cell(200, 10, txt="Recommended Models for Each Document Type", ln=True, align="L")

    pdf.set_font("Arial", size=12)
    for doc_type, (model, percentage) in best_models.items():
        pdf.cell(200, 10, txt=f"{doc_type}: {model}", ln=True, align="L")

    pdf.output(output_path)
    logging.info(f"PDF generated successfully and saved as {output_path}")


def generate_states_PDF(type):
    # Ensure the 'type' parameter is one of the allowed values
    if type.lower() not in ['after', 'before']:
        raise ValueError("Invalid value for 'type'. Expected 'after' or 'before'.")

    if type.lower() == 'before':
        ocr_pdf_dir = "../output_before_corr_txt/OCR_Model_Statistics_Before_Correction.pdf"
        docx_files = [
            '../output_before_corr_txt/earthMomma2_18k_tesseract_ocr.docx',
            '../output_before_corr_txt/earthMomma_5k_intr_tesseract_ocr.docx',
            '../output_before_corr_txt/earthmomma_lite_tesseract_ocr.docx',
            '../output_before_corr_txt/earthMomma1_tesseract_ocr.docx',
            '../output_before_corr_txt/ninetyseven1_tesseract_ocr.docx',
            '../output_before_corr_txt/ninetyseven2_tesseract_ocr.docx',
            '../output_before_corr_txt/ninetysevenlite_tesseract_ocr.docx',
            '../output_before_corr_txt/default_tesseract_ocr.docx'
        ]
    elif type.lower() == 'after':
        ocr_pdf_dir = "../output_after_corr_txt/OCR_Model_Statistics_After_Correction.pdf"
        docx_files = [
            '../output_after_corr_txt/earthMomma2_18k_corrected_ocr.docx',
            '../output_after_corr_txt/earthMomma_5k_intr_corrected_ocr.docx',
            '../output_after_corr_txt/earthmomma_lite_corrected_ocr.docx',
            '../output_after_corr_txt/earthMomma1_corrected_ocr.docx',
            '../output_after_corr_txt/ninetyseven1_corrected_ocr.docx',
            '../output_after_corr_txt/ninetyseven2_corrected_ocr.docx',
            '../output_after_corr_txt/ninetysevenlite_corrected_ocr.docx',
            '../output_after_corr_txt/default_corrected_ocr.docx'
        ]
    all_statistics = {}

    for docx_file in docx_files:
        statistics = extract_statistics_from_docx(docx_file)
        all_statistics.update(statistics)

    create_pdf(all_statistics, ocr_pdf_dir)


""" ------------------------------ Matching JSON Outputs - Generate Statistic as DOCX -------------------------------- """
""" ------------------------------------------------------------------------------------------------------------------ """


def load_json(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def normalize_text_josn(text):
    if text is None:
        return ""
    return str(text).strip().lower()


def calculate_match_json_percentage(original, trained):
    total_fields = 0
    matched_fields = 0

    for original_row, trained_row in zip(original, trained):
        for key in original_row.keys():
            original_value = normalize_text_josn(original_row.get(key))
            trained_value = normalize_text_josn(trained_row.get(key))
            total_fields += 1
            if original_value == trained_value:
                matched_fields += 1

    if total_fields == 0:
        return 0.0
    return (matched_fields / total_fields) * 100


# Extract only the "table" part for comparison
def extract_table_json(json_data):
    return json_data.get('table', [])


def create_docx_report(comparison_results, best_modules, output_path):
    doc = Document()
    doc.add_heading("OCR Model Comparison Statistics", level=1)

    for module, results in comparison_results.items():
        doc.add_heading(f'Module: {module}', level=2)
        for doc_type, percentage in results.items():
            doc.add_paragraph(f'{doc_type.upper()}: {percentage:.2f}%')

    doc.add_heading("Recommended Models for Each Document Type", level=1)
    for doc_type, module in best_modules.items():
        doc.add_paragraph(f'{doc_type.upper()}: {module}')

    doc.save(output_path)


def generate_states_Json_Docx():
    # Define paths
    trained_modules = [
        'earthMomma1',
        'earthMomma2_18k',
        'earthMomma_5k_intr',
        'ninetyseven1',
        'ninetyseven2',
        'ninetysevenlite',
        'earthmomma_lite',
        'default'
    ]

    document_types = ['cooper_pharma_casa', 'spr', 'gpm', 'sophadims', 'sophaca']

    output_dir = Path("../../../output_json/")
    original_dir = Path("../../../original_json/")

    # Load original JSON files
    original_jsons = {doc_type: load_json(original_dir / f'original_{doc_type}_result.json') for doc_type in
                      document_types}

    # Load trained module JSON files
    trained_jsons = {
        module: {doc_type: load_json(output_dir / f'{module.lower()}_{doc_type}_final_result.json') for doc_type in
                 document_types} for module in trained_modules}

    # Compare JSON files and calculate match percentages
    comparison_results = {
        module: {
            doc_type: calculate_match_json_percentage(extract_table_json(original_jsons[doc_type]),
                                                      extract_table_json(trained_jsons[module][doc_type]))
            for doc_type in document_types
        }
        for module in trained_modules
    }

    # Determine the best module for each document type
    best_modules = {doc_type: max(trained_modules, key=lambda module: comparison_results[module][doc_type]) for doc_type
                    in document_types}

    # Save the DOCX report
    output_docx_path = '../output_json/comparison_report.docx'
    create_docx_report(comparison_results, best_modules, output_docx_path)

    logging.info(f"DOCX report generated successfully: {output_docx_path}")


""" -------------------------------- Generate Matrix PDF with Statistic as PDF File ---------------------------------- """
""" ------------------------------------------------------------------------------------------------------------------ """


def _matrix_calculate_match_percentage(original, ocr_result):
    chars_to_remove = [',', '.']

    for char in chars_to_remove:
        original = original.replace(char, '')
        ocr_result = ocr_result.replace(char, '')

    matcher = SequenceMatcher(None, original.split(), ocr_result.split())
    match_ratio = matcher.ratio()
    return match_ratio * 100


def _matrix_extract_statistics_from_docx(docx_path):
    statistics = {}
    doc = Document(docx_path)
    model = os.path.basename(docx_path).replace('_tesseract_ocr.docx', '').replace('_corrected', '')

    current_doc_type = None

    for paragraph in doc.paragraphs:
        text = paragraph.text.strip()
        if "Processing model:" in text:
            current_doc_type = text.split("Processing model:")[1].split("(")[0].strip()
        if "Match Percentage:" in text:
            percentage = float(text.split("Match Percentage:")[1].split("%")[0].strip())
            if model not in statistics:
                statistics[model] = {}
            statistics[model][current_doc_type] = percentage

    return statistics


def _matrix_create_pdf(statistics, output_path):
    pdf = FPDF()
    pdf.add_page()
    pdf.set_font("Arial", size=12)

    pdf.cell(200, 10, txt="OCR Model Comparison Statistics", ln=True, align="C")

    best_models = {}

    for model, stats in statistics.items():
        pdf.ln(10)
        pdf.set_font("Arial", style='B', size=12)
        pdf.cell(200, 10, txt=model, ln=True, align="L")

        pdf.set_font("Arial", size=12)
        for doc_type, percentage in stats.items():
            pdf.cell(200, 10, txt=f"--- {doc_type} : {percentage:.2f}%", ln=True, align="L")
            if doc_type not in best_models or best_models[doc_type][1] < percentage:
                best_models[doc_type] = (model, percentage)

    pdf.ln(10)
    pdf.set_font("Arial", style='B', size=12)
    pdf.cell(200, 10, txt="Recommended Models for Each Document Type", ln=True, align="L")

    pdf.set_font("Arial", size=12)
    for doc_type, (model, percentage) in best_models.items():
        pdf.cell(200, 10, txt=f"{doc_type}: {model}", ln=True, align="L")

    pdf.output(output_path)
    logging.info(f"PDF generated successfully and saved as {output_path}")


def _matrix_create_matrix_pdf(statistics, output_path):
    pdf = FPDF(orientation='L', unit='mm', format='A3')  # 'L' for landscape, 'A3' for larger page size
    pdf.add_page()
    pdf.set_font("Arial", size=10)

    page_width = 420  # A3 landscape width in mm
    first_col_width = page_width * 0.25  # 25% of the page width
    other_col_width = page_width * 0.140  # 14.5% of the page width

    doc_types = list(next(iter(statistics.values())).keys())
    modules = list(statistics.keys())

    # Title
    pdf.cell(page_width, 10, txt="OCR Model Comparison Matrix", ln=True, align="C")
    pdf.ln(10)

    # Header Row
    pdf.set_font("Arial", style='B', size=10)
    pdf.cell(first_col_width, 10, txt="Module", border=1)
    for doc_type in doc_types:
        pdf.cell(other_col_width, 10, txt=doc_type, border=1)
    pdf.ln(10)

    # Data Rows
    pdf.set_font("Arial", size=10)
    best_models = {doc_type: max(modules, key=lambda module: statistics[module].get(doc_type, 0)) for doc_type in
                   doc_types}
    for module in modules:
        pdf.cell(first_col_width, 10, txt=module, border=1)
        for doc_type in doc_types:
            percentage = statistics[module].get(doc_type, 0)
            if module == best_models[doc_type]:
                pdf.set_fill_color(255, 255, 0)  # Yellow background
                pdf.cell(other_col_width, 10, txt=f"{percentage:.2f}%", border=1, fill=True)
            else:
                pdf.cell(other_col_width, 10, txt=f"{percentage:.2f}%", border=1)
        pdf.ln(10)

    # Recommended Row
    pdf.set_font("Arial", style='B', size=10)
    pdf.set_fill_color(0, 128, 0)  # Green background
    pdf.set_text_color(255, 255, 255)  # White text
    pdf.cell(first_col_width, 10, txt="Recommended", border=1, fill=True)
    for doc_type in doc_types:
        pdf.cell(other_col_width, 10, txt=best_models[doc_type], border=1, fill=True)
    pdf.ln(10)

    # Reset text color to black for other texts
    pdf.set_text_color(0, 0, 0)

    pdf.output(output_path)
    logging.info(f"Matrix PDF generated successfully and saved as {output_path}")


def _matrix_generate_states_PDF(type):
    if type.lower() not in ['after', 'before']:
        raise ValueError("Invalid value for 'type'. Expected 'after' or 'before'.")

    if type.lower() == 'before':
        ocr_pdf_dir = "../output_before_corr_txt/OCR_Model_Statistics_Before_Correction.pdf"
        matrix_pdf_dir = "../output_before_corr_txt/OCR_Model_Statistics_Matrix_Before_Correction.pdf"
        docx_files = [
            '../output_before_corr_txt/earthMomma2_18k_tesseract_ocr.docx',
            '../output_before_corr_txt/earthMomma_5k_intr_tesseract_ocr.docx',
            '../output_before_corr_txt/earthmomma_lite_tesseract_ocr.docx',
            '../output_before_corr_txt/earthMomma1_tesseract_ocr.docx',
            '../output_before_corr_txt/ninetyseven1_tesseract_ocr.docx',
            '../output_before_corr_txt/ninetyseven2_tesseract_ocr.docx',
            '../output_before_corr_txt/ninetysevenlite_tesseract_ocr.docx',
            '../output_before_corr_txt/default_tesseract_ocr.docx'
        ]
    elif type.lower() == 'after':
        ocr_pdf_dir = "../output_after_corr_txt/OCR_Model_Statistics_After_Correction.pdf"
        matrix_pdf_dir = "../output_after_corr_txt/OCR_Model_Statistics_Matrix_After_Correction.pdf"
        docx_files = [
            '../output_after_corr_txt/earthMomma2_18k_corrected_ocr.docx',
            '../output_after_corr_txt/earthMomma_5k_intr_corrected_ocr.docx',
            '../output_after_corr_txt/earthmomma_lite_corrected_ocr.docx',
            '../output_after_corr_txt/earthMomma1_corrected_ocr.docx',
            '../output_after_corr_txt/ninetyseven1_corrected_ocr.docx',
            '../output_after_corr_txt/ninetyseven2_corrected_ocr.docx',
            '../output_after_corr_txt/ninetysevenlite_corrected_ocr.docx',
            '../output_after_corr_txt/default_corrected_ocr.docx'
        ]

    all_statistics = {}
    for docx_file in docx_files:
        statistics = _matrix_extract_statistics_from_docx(docx_file)
        all_statistics.update(statistics)

    _matrix_create_pdf(all_statistics, ocr_pdf_dir)
    _matrix_create_matrix_pdf(all_statistics, matrix_pdf_dir)


""" ---------------------------------------- API Utils ---------------------------------------- """


def get_temp_path():
    # Assuming this function correctly calculates the path to the 'temp' directory
    return Path(__file__).parents[3] / "temp"


def get_base_name(path):
    return os.path.basename(path)


def load_image_from_url(url, output_dir_path, random_id):
    response = requests.get(url)
    try:
        if response.status_code == 200:
            image = Image.open(BytesIO(response.content))
            # Generate a unique filename based on URL and random_id
            filename = os.path.basename(url).split('.')[0] + '_origin_' + random_id + '.jpg'
            output_file_path = output_dir_path / filename
            image.save(output_file_path)
            return output_file_path
        else:
            logging.info("Failed to load image:", response.status_code)
            return None
    except Exception as e:
        logging.info("Error loading image:", e)
        return None


async def write_image_async(image_path_to_preprocessing, image_path_processed):
    loop = asyncio.get_running_loop()
    with ThreadPoolExecutor() as pool:
        await loop.run_in_executor(pool, cv2.imwrite, str(image_path_to_preprocessing),
                                   cv2.imread(str(image_path_processed)))


async def zip_image_async(output_dir_path, origin_images_path, random_id, image):
    """ Zip """
    # Save the uploaded zip file to the temporary directory
    zip_path = output_dir_path / f"uploaded_{random_id}.zip"
    async with aiofiles.open(zip_path, 'wb') as out_file:
        content = await image.read()
        await out_file.write(content)

    # Extract the image from the zip file
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        # Get the list of files in the zip archive
        extracted_files = zip_ref.namelist()
        if not extracted_files:
            raise HTTPException(status_code=400, detail="No files found in the zip.")
        # Extract and rename the first file to the desired name
        with zip_ref.open(extracted_files[0]) as extracted_file:
            image_content = extracted_file.read()
            image_name = image.filename.split('.')[0] + '_origin_' + random_id + '.jpg'
            image_path = output_dir_path / image_name
            image_origin_path = origin_images_path / image_name

            # Write the image content to the first path
            async with aiofiles.open(image_path, 'wb') as buffer:
                await buffer.write(image_content)

            # Write the image content to the second path
            async with aiofiles.open(image_origin_path, 'wb') as buffer:
                await buffer.write(image_content)

    return image_path


# async def pdf_image_async(output_dir_path, origin_images_path, random_id, image, isScanner: bool = False):
#     """Pdf"""
#     # Save the uploaded PDF file to the temporary directory
#     pdf = output_dir_path / f"uploaded_{random_id}.pdf"
#     async with aiofiles.open(pdf, 'wb') as out_file:
#         content = await image.read()
#         await out_file.write(content)
#
#     # Extract the image from the PDF file
#     pdf_document = fitz.open(pdf)
#     page = pdf_document.load_page(0)  # Only process the first page
#     pix = page.get_pixmap()
#     if isScanner:
#         image_name = 'img_originScanned_' + random_id + '.jpg'
#     else:
#         image_name = 'image_origin_' + random_id + '.jpg'
#     image_path = output_dir_path / image_name
#     pix.save(image_path)
#
#     if not image_path:
#         raise HTTPException(status_code=404, detail="No image found in the PDF.")
#
#     # Save the original image
#     image_origin_path = origin_images_path / image_name
#     async with aiofiles.open(image_origin_path, 'wb') as buffer:
#         async with aiofiles.open(image_path, 'rb') as image_file:
#             await buffer.write(await image_file.read())
#
#     # If the image is from a scanner, reduce lighting and save with a different name
#     if isScanner:
#         logging.info("isScanner __ 1 : ", isScanner)
#         scanned_image_name = 'image_origin_' + random_id + '.jpg'
#         scanned_image_path = origin_images_path / scanned_image_name
#         reduce_lighting(str(image_path), str(scanned_image_path))
#         return scanned_image_path
#
#     return image_path
#

async def pdf_image_async(output_dir_path, origin_images_path, random_id, image, isScanner: bool = False):
    """Handle Image"""
    # Save the uploaded image file to the temporary directory
    if isScanner:
        image_name = 'img_originScanned_' + random_id + '.jpg'
    else:
        image_name = 'image_origin_' + random_id + '.jpg'
    image_path = output_dir_path / image_name

    async with aiofiles.open(image_path, 'wb') as out_file:
        content = await image.read()
        await out_file.write(content)

    # Ensure the image file was saved
    if not image_path.exists():
        raise HTTPException(status_code=404, detail="Image file could not be saved.")

    # Save the original image to the origin images path
    image_origin_path = origin_images_path / image_name
    async with aiofiles.open(image_origin_path, 'wb') as buffer:
        async with aiofiles.open(image_path, 'rb') as image_file:
            await buffer.write(await image_file.read())

    # If the image is from a scanner, reduce lighting and save with a different name
    if isScanner:
        scanned_image_name = 'image_origin_' + random_id + '.jpg'
        scanned_image_path = origin_images_path / scanned_image_name
        reduce_lighting(str(image_path), str(scanned_image_path))
        return scanned_image_path

    return image_path


# To reduce the Lighting and the contrast for an Image Scanned
def reduce_lighting(image_path, output_path):
    # Read the image
    image = cv2.imread(image_path)

    if image is None:
        logging.info("Error: Image not found at the specified path.")
        return

    # Convert the image to the LAB color space
    lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(lab)

    # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization) to the L-channel
    clahe = cv2.createCLAHE(clipLimit=1.0, tileGridSize=(8, 8))
    l_equalized = clahe.apply(l)

    # Merge the CLAHE enhanced L-channel back with A and B channels
    lab_equalized = cv2.merge((l_equalized, a, b))

    # Convert the LAB image back to the BGR color space
    result = cv2.cvtColor(lab_equalized, cv2.COLOR_LAB2BGR)

    # Optionally, apply gamma correction to reduce lighting further
    gamma = 0.8  # Adjust gamma value as needed (<1 for darker, >1 for brighter)
    inv_gamma = 1.0 / gamma
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
    result = cv2.LUT(result, table)

    # Reduce contrast by blending with a neutral gray image
    alpha = 1  # Alpha controls the intensity of contrast reduction (0.0 to 1.0)
    gray = np.full_like(result, 128)  # Create a neutral gray image
    result = cv2.addWeighted(result, alpha, gray, 0.9 - alpha, 0)

    # Save the output image
    cv2.imwrite(output_path, result)
    logging.info(f"Processed image saved at {output_path}")
