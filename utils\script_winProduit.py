import pandas as pd
import re

# Read the Excel file
winproduits = pd.read_excel('Winproduits_V3_12-07-2024.xlsx')

# Define the column mapping
mapping_produits = {
    'code_prd': 'id',
    'categorie': 'categorie_libelle',
    'forme_galenique': 'forme_libelle',
    'designation': 'libelle_produit',
    'code_barre': 'code_barre',
    'prix_vente_std': 'ppv',
    'tva_label': 'taux_tva',
    'prix_achat_std': 'prix_vente_ttc'
}

# Rename the columns based on the mapping
new_produits = winproduits.rename(columns=mapping_produits)

# Add missing columns with NA values if they do not exist
for column in ['catalogue_id', 'code_produit_catalogue', 'date_creation', 'colisage', 'prix_vente_ht', 'societe_id']:
    if column not in new_produits.columns:
        new_produits[column] = pd.NA

# Convert 'code_barre' column to string
new_produits['code_barre'] = new_produits['code_barre'].astype(str)

# Extract the percentage value from 'tva_label'
new_produits['taux_tva'] = new_produits['taux_tva'].apply(lambda x: re.search(r'\((.*?)%\)', x).group(1) if pd.notna(x) and re.search(r'\((.*?)%\)', x) else pd.NA)

# Add the 'laboratoire_label' column
new_produits['laboratoire_label'] = winproduits['laboratoire_label']

# Define the desired column order
columns_order = ['id', 'catalogue_id', 'code_produit_catalogue', 'libelle_produit', 'taux_tva', 'prix_vente_ht',
                 'prix_vente_ttc', 'ppv', 'societe_id', 'code_barre', 'categorie_libelle', 'forme_libelle',
                 'date_creation', 'colisage', 'laboratoire_label']

# Reorder the columns
new_produits = new_produits[columns_order]

# Export the DataFrame to a CSV file with UTF-8 encoding
new_produits.to_csv('nouveau_produits.csv', index=False, encoding='utf-8-sig', sep='|')

# Print the total number of rows in the DataFrame
total_lignes = new_produits.shape[0]
logging.info(f"Total des lignes dans le fichier de produits: {total_lignes}")
