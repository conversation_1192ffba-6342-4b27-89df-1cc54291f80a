# USAGE:
# python smart_crop.py (--images <IMG_DIR> | --image <IMG_PATH>) [-i]
# For example, to scan a single image with interactive mode:
# python smart_crop.py --image sample_images/desk.JPG -i
# To scan all images in a directory automatically:
# python smart_crop.py --images sample_images

# Scanned images will be output to directory named 'output'

from pyimagesearch import transform
from pyimagesearch import imutils
from scipy.spatial import distance as dist
from matplotlib.patches import Polygon
# import polygon_interacter as poly_i
import polygon_interacter as poly_i
import numpy as np
import matplotlib.pyplot as plt
import itertools
import math
import cv2
import uuid
import argparse
import os
from pathlib import Path
import logging
import traceback
from fastapi import HTTPException
from PIL import Image
import numpy as np

class DocScanner(object):
    """An image scanner"""

    def __init__(self, interactive=False, MIN_QUAD_AREA_RATIO=0.25, MAX_QUAD_ANGLE_RANGE=40, random_id=None, output_dir='output'):
        """
        Args:
            interactive (boolean): If True, user can adjust screen contour before
                transformation occurs in interactive pyplot window.
            MIN_QUAD_AREA_RATIO (float): A contour will be rejected if its corners 
                do not form a quadrilateral that covers at least MIN_QUAD_AREA_RATIO 
                of the original image. Defaults to 0.25.
            MAX_QUAD_ANGLE_RANGE (int):  A contour will also be rejected if the range 
                of its interior angles exceeds MAX_QUAD_ANGLE_RANGE. Defaults to 40.
        """        
        self.interactive = interactive
        self.MIN_QUAD_AREA_RATIO = MIN_QUAD_AREA_RATIO
        self.MAX_QUAD_ANGLE_RANGE = MAX_QUAD_ANGLE_RANGE
        self.random_id = random_id  # Store the random_id as an instance variable
        self.output_dir = Path(output_dir)

    def filter_corners(self, corners, min_dist=20):
        """Filters corners that are within min_dist of others"""
        def predicate(representatives, corner):
            return all(dist.euclidean(representative, corner) >= min_dist
                       for representative in representatives)

        filtered_corners = []
        for c in corners:
            if predicate(filtered_corners, c):
                filtered_corners.append(c)
        return filtered_corners

    def angle_between_vectors_degrees(self, u, v):
        """Returns the angle between two vectors in degrees"""
        return np.degrees(
            math.acos(np.dot(u, v) / (np.linalg.norm(u) * np.linalg.norm(v))))

    def get_angle(self, p1, p2, p3):
        """
        Returns the angle between the line segment from p2 to p1 
        and the line segment from p2 to p3 in degrees
        """
        a = np.radians(np.array(p1))
        b = np.radians(np.array(p2))
        c = np.radians(np.array(p3))

        avec = a - b
        cvec = c - b

        return self.angle_between_vectors_degrees(avec, cvec)

    def angle_range(self, quad):
        """
        Returns the range between max and min interior angles of quadrilateral.
        The input quadrilateral must be a numpy array with vertices ordered clockwise
        starting with the top left vertex.
        """
        tl, tr, br, bl = quad
        ura = self.get_angle(tl[0], tr[0], br[0])
        ula = self.get_angle(bl[0], tl[0], tr[0])
        lra = self.get_angle(tr[0], br[0], bl[0])
        lla = self.get_angle(br[0], bl[0], tl[0])

        angles = [ura, ula, lra, lla]
        return np.ptp(angles)          

    def get_corners(self, img):
        """
        Returns a list of corners ((x, y) tuples) found in the input image. With proper
        pre-processing and filtering, it should output at most 10 potential corners.
        This is a utility function used by get_contours. The input image is expected
        to be rescaled and Canny filtered prior to be passed in.
        """
        lsd = cv2.createLineSegmentDetector()
        lines = lsd.detect(img)[0]

        corners = []
        if lines is not None:
            # separate out the horizontal and vertical lines, and draw them back onto separate canvases
            lines = lines.squeeze().astype(np.int32).tolist()
            horizontal_lines_canvas = np.zeros(img.shape, dtype=np.uint8)
            vertical_lines_canvas = np.zeros(img.shape, dtype=np.uint8)
            for line in lines:
                x1, y1, x2, y2 = line
                if abs(x2 - x1) > abs(y2 - y1):
                    (x1, y1), (x2, y2) = sorted(((x1, y1), (x2, y2)), key=lambda pt: pt[0])
                    cv2.line(horizontal_lines_canvas, (max(x1 - 5, 0), y1), (min(x2 + 5, img.shape[1] - 1), y2), 255, 2)
                else:
                    (x1, y1), (x2, y2) = sorted(((x1, y1), (x2, y2)), key=lambda pt: pt[1])
                    cv2.line(vertical_lines_canvas, (x1, max(y1 - 5, 0)), (x2, min(y2 + 5, img.shape[0] - 1)), 255, 2)

            lines = []

            # find the horizontal lines (connected-components -> bounding boxes -> final lines)
            contours, _ = cv2.findContours(horizontal_lines_canvas, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
            contours = sorted(contours, key=lambda c: cv2.arcLength(c, True), reverse=True)[:2]
            horizontal_lines_canvas = np.zeros(img.shape, dtype=np.uint8)
            for contour in contours:
                contour = contour.reshape((contour.shape[0], contour.shape[2]))
                min_x = np.amin(contour[:, 0], axis=0) + 2
                max_x = np.amax(contour[:, 0], axis=0) - 2
                left_y = int(np.average(contour[contour[:, 0] == min_x][:, 1]))
                right_y = int(np.average(contour[contour[:, 0] == max_x][:, 1]))
                lines.append((min_x, left_y, max_x, right_y))
                cv2.line(horizontal_lines_canvas, (min_x, left_y), (max_x, right_y), 1, 1)
                corners.append((min_x, left_y))
                corners.append((max_x, right_y))

            # find the vertical lines (connected-components -> bounding boxes -> final lines)
            contours, _ = cv2.findContours(vertical_lines_canvas, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
            contours = sorted(contours, key=lambda c: cv2.arcLength(c, True), reverse=True)[:2]
            vertical_lines_canvas = np.zeros(img.shape, dtype=np.uint8)
            for contour in contours:
                contour = contour.reshape((contour.shape[0], contour.shape[2]))
                min_y = np.amin(contour[:, 1], axis=0) + 2
                max_y = np.amax(contour[:, 1], axis=0) - 2
                top_x = int(np.average(contour[contour[:, 1] == min_y][:, 0]))
                bottom_x = int(np.average(contour[contour[:, 1] == max_y][:, 0]))
                lines.append((top_x, min_y, bottom_x, max_y))
                cv2.line(vertical_lines_canvas, (top_x, min_y), (bottom_x, max_y), 1, 1)
                corners.append((top_x, min_y))
                corners.append((bottom_x, max_y))

            # find the corners
            corners_y, corners_x = np.where(horizontal_lines_canvas + vertical_lines_canvas == 2)
            corners += zip(corners_x, corners_y)

        # remove corners in close proximity
        corners = self.filter_corners(corners)
        return corners

    def is_valid_contour(self, cnt, IM_WIDTH, IM_HEIGHT):
        """Returns True if the contour satisfies all requirements set at instantitation"""

        return (len(cnt) == 4 and cv2.contourArea(cnt) > IM_WIDTH * IM_HEIGHT * self.MIN_QUAD_AREA_RATIO 
            and self.angle_range(cnt) < self.MAX_QUAD_ANGLE_RANGE)

    def get_contour(self, rescaled_image):
        """
        Returns a numpy array of shape (4, 2) containing the vertices of the four corners
        of the document in the image. It considers the corners returned from get_corners()
        and uses heuristics to choose the four corners that most likely represent
        the corners of the document. If no corners were found, or the four corners represent
        a quadrilateral that is too small or convex, it returns the original four corners.
        """

        # these constants are carefully chosen
        MORPH = 9
        CANNY = 84
        HOUGH = 25

        IM_HEIGHT, IM_WIDTH, _ = rescaled_image.shape

        # convert the image to grayscale and blur it slightly
        gray = cv2.cvtColor(rescaled_image, cv2.COLOR_BGR2GRAY)

        # Use multiple preprocessing techniques to enhance edges
        # 1. Gaussian Blur
        gray = cv2.GaussianBlur(gray, (7, 7), 0)

        # 2. Adaptive Thresholding to create more contrast
        thresh = cv2.adaptiveThreshold(
            gray, 255,
            cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY_INV,
            11, 2
        )

        # dilate helps to remove potential holes between edge segments
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (MORPH, MORPH))
        dilated = cv2.dilate(gray, kernel)

        # find edges and mark them in the output map using the Canny algorithm
        edged = cv2.Canny(dilated, 0, CANNY)
        test_corners = self.get_corners(edged)

        # edged_pil = Image.fromarray(edged)
        # edged_pil.show()
        #
        # dilated_pil = Image.fromarray(dilated)
        # dilated_pil.show()

        approx_contours = []

        if len(test_corners) >= 4:
            quads = []

            for quad in itertools.combinations(test_corners, 4):
                points = np.array(quad)
                points = transform.order_points(points)
                points = np.array([[p] for p in points], dtype="int32")
                quads.append(points)

            # get top five quadrilaterals by area
            quads = sorted(quads, key=cv2.contourArea, reverse=True)[:5]
            # sort candidate quadrilaterals by their angle range, which helps remove outliers
            quads = sorted(quads, key=self.angle_range)

            approx = quads[0]
            if self.is_valid_contour(approx, IM_WIDTH, IM_HEIGHT):
                approx_contours.append(approx)

        # also attempt to find contours directly from the edged image, which occasionally
        # produces better results
        cnts, _ = cv2.findContours(edged.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cnts = sorted(cnts, key=cv2.contourArea, reverse=True)[:5]

        # loop over the contours
        for c in cnts:
            # approximate the contour
            approx = cv2.approxPolyDP(c, 80, True)
            if self.is_valid_contour(approx, IM_WIDTH, IM_HEIGHT):
                approx_contours.append(approx)
                break

        # If we did not find any valid contours, just use the whole image
        if not approx_contours:
            TOP_RIGHT = (IM_WIDTH, 0)
            BOTTOM_RIGHT = (IM_WIDTH, IM_HEIGHT)
            BOTTOM_LEFT = (0, IM_HEIGHT)
            TOP_LEFT = (0, 0)
            screenCnt = np.array([[TOP_RIGHT], [BOTTOM_RIGHT], [BOTTOM_LEFT], [TOP_LEFT]])

        else:
            screenCnt = max(approx_contours, key=cv2.contourArea)

        return screenCnt.reshape(4, 2)

    def interactive_get_contour(self, screenCnt, rescaled_image):
        poly = Polygon(screenCnt, animated=True, fill=False, color="yellow", linewidth=5)
        fig, ax = plt.subplots()
        ax.add_patch(poly)
        ax.set_title(('Drag the corners of the box to the corners of the document. \n'
            'Close the window when finished.'))
        p = poly_i.PolygonInteractor(ax, poly)
        # plt.imshow(rescaled_image)
        # plt.show()

        new_points = p.get_poly_points()[:4]
        new_points = np.array([[p] for p in new_points], dtype = "int32")
        return new_points.reshape(4, 2)

    def scan(self, image_path, isScanner=False):

        try:
            RESCALED_HEIGHT = 500.0
            OUTPUT_DIR = Path(self.output_dir)
            OUTPUT_DIR.mkdir(parents=True, exist_ok=True)  # Ensure directory exists

            # load the image and compute the ratio of the old height
            # to the new height, clone it, and resize it
            # image = cv2.imread(image_path)
            image = cv2.imread(str(image_path))

            if image is None:
                raise FileNotFoundError(f"Fichier image introuvable à l'adresse: {image_path}")

            ratio = image.shape[0] / RESCALED_HEIGHT
            orig = image.copy()
            rescaled_image = imutils.resize(image, height=int(RESCALED_HEIGHT))

            # Debug print
            logging.info(f"isScanner: {isScanner}")



            # If the image is from a scanner, return the full image coordinates
            if isScanner:
                IM_HEIGHT, IM_WIDTH = image.shape[:2]
                default_coordinates = [
                    [0, 0],  # Top-left
                    [IM_WIDTH, 0],  # Top-right
                    [IM_WIDTH, IM_HEIGHT],  # Bottom-right
                    [0, IM_HEIGHT]  # Bottom-left
                ]
                return default_coordinates  # Directly return full image coordinates

            # get the contour of the document
            screenCnt = self.get_contour(rescaled_image)

            if self.interactive:
                screenCnt = self.interactive_get_contour(screenCnt, rescaled_image)

            # apply the perspective transformation
            warped = transform.four_point_transform(orig, screenCnt * ratio)

            # convert the warped image to grayscale
            gray = cv2.cvtColor(warped, cv2.COLOR_BGR2GRAY)

            # sharpen image
            sharpen = cv2.GaussianBlur(gray, (0,0), 3)
            sharpen = cv2.addWeighted(gray, 1.5, sharpen, -0.5, 0)

            # apply adaptive threshold to get black and white effect
            thresh = cv2.adaptiveThreshold(sharpen, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 21, 15)

            # save the transformed image
            basename = os.path.basename(image_path)
            # cv2.imwrite(OUTPUT_DIR + '/' + basename + "_" + self.random_id , thresh)

            # save the transformed image
            processed_image = thresh  # This should be replaced with your actual processing logic
            cropped_image_name = f"cropped_locally_{self.random_id}.jpg"
            cropped_path = OUTPUT_DIR / cropped_image_name
            # Attempt to save the processed image
            # try:
            #     # success = cv2.imwrite(str(cropped_path), processed_image)
            #     success = cv2.imwrite(str(cropped_path), warped)
            #     if success:
            #         logging.info(f"Processed and saved: {cropped_path}")
            #     else:
            #         logging.info(f"Failed to save the image at {cropped_path}")
            # except Exception as e:
            #     logging.info(f"An error occurred while saving the image: {e}")

            # return the coordinates of the contour
            coordinates = screenCnt * ratio

            # self.deskew_image(cropped_path, coordinates)
            return coordinates.tolist()


        except FileNotFoundError as fnf_error:
            logging.error(f"Erreur de fichier: {fnf_error}")
            raise HTTPException(status_code=404, detail=str(fnf_error))
        except Exception as e:
            logging.error(f"Une erreur est survenue dans la méthode scan: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="Erreur interne du serveur lors du traitement de l'image")

    def crop_imagewith_cv2(self, coordinates, random_id, image_origin_path, output_dir_crop_img_path, rotate_degree=0):
        """
        Crop and rotate an image based on coordinates and rotation angle.

        Args:
            coordinates: List of coordinates for cropping
            random_id: Unique identifier for the output file
            image_origin_path: Path to the original image
            output_dir_crop_img_path: Output directory path
            rotate_degree: Angle in degrees to rotate the image (default: 0)

        Returns:
            tuple: (Path to cropped image, Cropped image array)
        """

        logging.info("Loading image from path: %s", image_origin_path)

        # Load the original image
        image = cv2.imread(str(image_origin_path))

        if image is None:
            logging.error("Failed to load image from path: %s", image_origin_path)
            raise FileNotFoundError(f"Image file not found at path: {image_origin_path}")

        logging.info("Image successfully loaded from path: %s", image_origin_path)

        # Rotate the image if needed
        if rotate_degree != 0:
            height, width = image.shape[:2]
            center = (width // 2, height // 2)

            # Create rotation matrix
            rotation_matrix = cv2.getRotationMatrix2D(center, -rotate_degree, 1.0)

            # Calculate new dimensions after rotation
            abs_cos = abs(rotation_matrix[0, 0])
            abs_sin = abs(rotation_matrix[0, 1])
            new_width = int(height * abs_sin + width * abs_cos)
            new_height = int(height * abs_cos + width * abs_sin)

            # Adjust rotation matrix for translation
            rotation_matrix[0, 2] += new_width / 2 - center[0]
            rotation_matrix[1, 2] += new_height / 2 - center[1]

            # Rotate the image
            image = cv2.warpAffine(image, rotation_matrix, (new_width, new_height), flags=cv2.INTER_LINEAR)

            # Adjust the crop coordinates to match the rotated image
            ones = np.ones((len(coordinates), 1))
            coordinates_with_ones = np.hstack([coordinates, ones])
            coordinates = cv2.transform(np.array([coordinates_with_ones]), rotation_matrix)[0]

            logging.info(f"Image rotated by {rotate_degree} degrees")

        # Convert the updated coordinates to a numpy array
        coordinates = np.array(coordinates, dtype="float32")

        # Apply the perspective transformation to crop the image
        cropped_image = transform.four_point_transform(image, coordinates)

        # Save the cropped image with high quality
        cropped_image_name = f"cropped_{random_id}.jpg"
        cropped_path = output_dir_crop_img_path / cropped_image_name

        # Save with high JPEG quality
        success = cv2.imwrite(
            str(cropped_path),
            cropped_image,
            [cv2.IMWRITE_JPEG_QUALITY, 95]  # Save with 95% quality
        )

        if not success:
            logging.error("Failed to save the cropped image")
            raise IOError(f"Could not save the cropped image at {cropped_path}")

        logging.info("Cropped image saved at: %s", cropped_path)

        return cropped_path, cropped_image


    def crop_image(self, coordinates, random_id, image_origin_path, output_dir_crop_img_path,
                               rotate_degree=0):
        """
        Crop and rotate an image based on coordinates and rotation angle using Pillow for high-quality saving.

        Args:
            coordinates: List of coordinates for cropping
            random_id: Unique identifier for the output file
            image_origin_path: Path to the original image
            output_dir_crop_img_path: Output directory path
            rotate_degree: Angle in degrees to rotate the image (default: 0)

        Returns:
            tuple: (Path to cropped image, Cropped image array)
        """

        logging.info("Loading image from path: %s", image_origin_path)

        # Load the original image using OpenCV
        image = cv2.imread(str(image_origin_path))

        if image is None:
            logging.error("Failed to load image from path: %s", image_origin_path)
            raise FileNotFoundError(f"Image file not found at path: {image_origin_path}")

        logging.info("Image successfully loaded from path: %s", image_origin_path)


        # Convert the updated coordinates to a numpy array
        coordinates = np.array(coordinates, dtype="float32")

        # Apply the perspective transformation to crop the image
        cropped_image = transform.four_point_transform(image, coordinates)

        # Convert cropped image from OpenCV to Pillow
        cropped_image_pil = Image.fromarray(cv2.cvtColor(cropped_image, cv2.COLOR_BGR2RGB))

        # Rotate the image using Pillow if a rotation degree is specified
        if rotate_degree != 0:
            # Rotate around the center using Pillow's built-in rotate
            cropped_image_pil = cropped_image_pil.rotate(-rotate_degree, resample=Image.BICUBIC, expand=True)
            logging.info("Image rotated by %s degrees around the center", -rotate_degree)

        # Save the cropped image using Pillow
        cropped_image_name = f"cropped_{random_id}.jpg"
        cropped_path = output_dir_crop_img_path / cropped_image_name

        # Use Pillow to save with maximum quality
        cropped_image_pil.save(cropped_path, format="JPEG", quality=100, optimize=False)

        logging.info("Cropped image saved at: %s", cropped_path)

        return cropped_path, cropped_image_pil


def get_temp_path():
    # Assuming this function correctly calculates the path to the 'temp' directory
    return Path(__file__).parents[2] / "temp"


# if __name__ == "__main__":
#     # Generate a random UUID for the image filename
#     random_id = str(uuid.uuid4())[:8]  # Extract the first 8 characters of the UUID
#     # # Scan Locally
#     interactive_mode = True
#     im_file_path = 'images/model01.jpeg'
#     output_dir_path = get_temp_path() / "smart_crop_output"
#     scanner = DocScanner(interactive=interactive_mode, random_id=random_id, output_dir=output_dir_path)
#     coordinates = scanner.scan(im_file_path)
#
#     logging.info('coordinates', coordinates)



if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    group = ap.add_mutually_exclusive_group(required=True)
    group.add_argument("--images", help="Directory of images to be scanned")
    group.add_argument("--image", help="Path to single image to be scanned")
    ap.add_argument("-i", action='store_true',
        help = "Flag for manually verifying and/or setting document corners")

    args = vars(ap.parse_args())
    im_dir = args["images"]
    im_file_path = args["image"]
    interactive_mode = args["i"]

    scanner = DocScanner(interactive_mode)

    valid_formats = [".jpg", ".jpeg", ".jp2", ".png", ".bmp", ".tiff", ".tif"]

    get_ext = lambda f: os.path.splitext(f)[1].lower()

    # Scan single image specified by command line argument --image <IMAGE_PATH>
    if im_file_path:
        coordinates = scanner.scan(im_file_path)
        logging.info("Coordinates of the document corners:", coordinates)


    # Scan all valid images in directory specified by command line argument --images <IMAGE_DIR>
    else:
        im_files = [f for f in os.listdir(im_dir) if get_ext(f) in valid_formats]
        for im in im_files:
            coordinates = scanner.scan(im_dir + '/' + im)

    # # Scan Locally
    interactive_mode = True
    im_file_path = 'images/model06.jpeg'
    scanner = DocScanner(interactive_mode)
    scanner.scan(im_file_path)