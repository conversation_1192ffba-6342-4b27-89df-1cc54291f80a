import sqlite3
import os

# Use the same DB_PATH as defined in your init script
DB_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'database')
DB_FILE = 'ocr_document_grossiste.db'
DB_PATH = os.path.join(DB_DIR, DB_FILE)

def init_db():
    # Create the database file if it doesn't exist
    if not os.path.exists(DB_DIR):
        os.makedirs(DB_DIR)

    logging.info(f"Creating database file at {DB_PATH}")

    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('''CREATE TABLE IF NOT EXISTS pre_bl_ocr
                                 (ID_BL INTEGER PRIMARY KEY AUTOINCREMENT,
                                  Content JSON,
                                  ID_USER TEXT,
                                  status TEXT DEFAULT 'EN_ATTENTE',
                                  ID_TENANT TEXT,
                                  CODE_TENANT TEXT,
                                  date TEXT DEFAULT CURRENT_TIMESTAMP,
                                  id_BL_origine TEXT DEFAULT NULL,
                                  date_BL_origine TEXT DEFAULT NULL,
                                  supplier_name TEXT DEFAULT NULL,
                                  supplier_id TEXT DEFAULT NULL,
                                  random_id TEXT DEFAULT NULL 
                                  )''')
    conn.commit()
    conn.close()

if __name__ == "__main__":
    init_db()
    logging.info("Database and table created successfully.")