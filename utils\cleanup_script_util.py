import os
import time
from pathlib import Path
import logging
from datetime import datetime

# Define the directories to clean
directories = {
    "columns": True,
    "compare_images": True,
    "identify_suppliers": True,
    "magic_pro_filter_output": True,
    "smart_crop_output": True,
    "output_preprocessing": False,  # Special conditions apply
}

# Configure the logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(str(Path(__file__).parents[1] / "cleanup_script.log")),
        logging.StreamHandler()
    ]
)


def log_info(message):
    logging.info(message)


def log_error(message):
    logging.error(message)


def log_debug(message):
    logging.debug(message)


# Define the root path
def get_temp_path():
    # Assuming this function correctly calculates the path to the 'temp' directory
    return Path(__file__).parents[1] / "temp"


# Calculate the time threshold (7 days ago)
# time_threshold = time.time() - 1 * 86400  # 1 day * 86400 seconds/day
# time_threshold = time.time() - 60  # 7 days * 86400 seconds/day


def should_not_delete_output_preprocessing(filename):
    return "origin" in filename or "final_format" in filename

# Function to delete files
def delete_files(dir_path, special_condition=True):
    for filename in os.listdir(dir_path):
        file_path = os.path.join(dir_path, filename)
        if os.path.isfile(file_path) and filename != ".gitkeep":
            file_mtime = os.path.getmtime(file_path)
            # if file_mtime < time_threshold:
            if True:
                if not special_condition:
                    if not should_not_delete_output_preprocessing(filename):
                        os.remove(file_path)
                        log_info(f"Deleted {file_path}")
                else:
                    os.remove(file_path)
                    log_info(f"Deleted {file_path}")


# Remove files at the same level as temp/
def delete_files_at_root_level():
    keywords = ['trimmed', 'cropper', 'annotated']
    for filename in os.listdir(get_temp_path()):
        file_path = get_temp_path() / filename
        if os.path.isfile(file_path):
            if any(keyword in filename for keyword in keywords):
                file_mtime = os.path.getmtime(file_path)
                # if file_mtime < time_threshold:
                if True:
                    os.remove(file_path)
                    log_info(f"Deleted {file_path}")


# Iterate through directories and delete files
for dir_name, special_condition in directories.items():
    dir_path = os.path.join(get_temp_path(), dir_name)
    if os.path.exists(dir_path):
        delete_files(dir_path, special_condition)
    else:
        log_error(f"Directory {dir_path} does not exist")

# Delete files at root level
try:
    delete_files_at_root_level()
except Exception as e:
    log_error(f"Error deleting files at root level: {e}")
