from fastapi import APIRouter, File, UploadFile, Form, HTTPException, WebSocketDisconnect, Depends
import uuid
import traceback
import asyncio
import logging
import sys
from src.app.services.websocket_manager import manager
from src.app.utils.helpers import get_temp_path
from src.app.utils.models import ModelName
from src.app.services import identify_supplier
from src.app.dependencies import get_current_user, check_headers
from concurrent.futures import ThreadPoolExecutor

router = APIRouter()

logger = logging.getLogger(__name__)

# Initialize a ThreadPoolExecutor
executor = ThreadPoolExecutor(max_workers=10)

@router.post("/")
async def identify_supplier_endpoint(image: UploadFile = File(...), model_name: ModelName = Form(...),
                                     random_id: str = None, job_id: str = Form(...)):
    try:

        # Log the entire request in one line
        logging.info(f"Received request from process_image_supp_endpoint")
        logging.info(f"image: {image}")
        logging.info(f"model_name: {model_name}")
        logging.info(f"random_id: {random_id}")
        logging.info(f"job_id: {job_id}")

        # Run `process_identify_supplier` in a ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            lambda: asyncio.run(identify_supplier_process(image, model_name, random_id, job_id))
        )

        # Return the result
        return result

    except HTTPException as http_exc:
        logging.error(f"Erreur HTTP: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logging.error(f"Erreur inattendue: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")


async def identify_supplier_process(image: UploadFile = File(...), model_name: ModelName = Form(...),
                                     random_id: int = None, job_id: str = Form(...)):

    try:
        # Generate a random UUID for the image filename
        if not random_id:
            random_id = str(uuid.uuid4())[:8]  # Extract the first 8 characters of the UUID

        output_dir_path_identify_supplier = get_temp_path() / "identify_suppliers"
        output_dir_path_identify_supplier.mkdir(exist_ok=True)

        # Save the uploaded image to the temporary directory
        image_name = image.filename.split('.')[0] + '_origin_' + random_id + '.jpg'
        image_path = output_dir_path_identify_supplier / f"{image_name}"
        with open(image_path, "wb") as buffer:
            buffer.write(await image.read())

        # Identify Supplier
        document_header = identify_supplier.identify_supplier(image_path, model_name.upper(),
                                                              output_dir_path_identify_supplier, random_id)
        supplier_name = document_header.header.name_fournisseur

        # Check if the OCR result matches the expected model_name
        if supplier_name.upper() not in [model.value for model in ModelName]:
            return {
                "warning": "Please try again, as we are unable to locate the supplier of this document."
            }

        # Notify the client about progress (100% complete)
        try:
            await manager.send_progress(job_id, 100)
            await asyncio.sleep(0.01)
        except (WebSocketDisconnect, RuntimeError) as e:
            logger.error(f"Error sending progress update: {e}")

        # Merge the Python objects into a single dictionary
        data = {
            'supplier_name': supplier_name
        }
        return data

    except HTTPException as http_exc:
        logger.error(f"Erreur HTTP: {http_exc.detail}")
        raise http_exc
    except FileNotFoundError as fnf_error:
        logger.error(f"Erreur de fichier: {fnf_error}")
        raise HTTPException(status_code=404, detail="Le fichier image n'a pas été trouvé.")
    except ValueError as val_error:
        logger.error(f"Erreur de valeur: {val_error}")
        raise HTTPException(status_code=400, detail="Valeur incorrecte fournie.")
    except Exception as e:
        logger.error(f"Erreur inattendue: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")

