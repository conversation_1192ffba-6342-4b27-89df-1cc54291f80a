from fastapi import APIRouter, File, UploadFile, Form, HTTPException, WebSocketDisconnect, Head<PERSON>
import logging
from ..config import API_URL, WINPLUS_URL, TAP_URL
from ..utils.helpers import get_temp_path
from concurrent.futures import ThreadPoolExecutor
import asyncio
import traceback
import uuid
import aiofiles
from src.app.services.websocket_manager import manager
from src.app.services.ocr_service import OCRService
from src.app.services.medication_processor import process_medication_text


router = APIRouter()
logger = logging.getLogger(__name__)

# Define the URLs
apiUrl = API_URL
tapUrl = TAP_URL
winPlusUrl = WINPLUS_URL

# Initialize a ThreadPoolExecutor
executor = ThreadPoolExecutor(max_workers=10)

# Initialize OCR service at the top of the file after imports
ocr_service = OCRService()

@router.post("/")
async def get_info_medicament_and_suggestions(
    image: UploadFile = File(...),
    job_id: str = Form(...),
    Authorization: str = Header(...),
    AuthorizationTenant: str = Header(...),
    AuthorizationUser: str = Header(...)
):
    try:
        # Run `process_identify_supplier` in a ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            lambda: asyncio.run(get_info_medicament_and_suggestions_process(
                image,
                job_id,
                Authorization,
                AuthorizationTenant,
                AuthorizationUser
            ))
        )

        # Return the result
        return result

    except HTTPException as http_exc:
        logging.error(f"Erreur HTTP: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logging.error(f"Erreur inattendue: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")


async def get_info_medicament_and_suggestions_process(image: UploadFile = File(...), job_id: str = Form(...),
                                                      Authorization: str = Header(...),
                                                      AuthorizationTenant: str = Header(...),
                                                      AuthorizationUser: str = Header(...)):
    try:
        # Temporary directory for processed files
        output_dir_path = get_temp_path() / "medicament_ocr_output"
        output_dir_path.mkdir(exist_ok=True, parents=True)

        # Generate a random UUID for the image filename
        random_id = str(uuid.uuid4())[:8]  # Extract the first 8 characters of the UUID

        """ Save Image """
        image_path = await save_image_async_medic(output_dir_path, random_id, image)
        """ Save Image """

        # Notify the client about progress (30% complete)
        try:
            await manager.send_progress(job_id, 30)
            await asyncio.sleep(0.01)
        except (WebSocketDisconnect, RuntimeError) as e:
            logger.error(f"Error sending progress update: {e}")

        # Call OCR service to extract text from the image
        ocr_result = ocr_service.extract_text(image_path, use_row_threshold=False)
        ocr_text = " ".join(ocr_result.raw_lines)

        # Process the OCR text to get a structured medication name
        processed_text = process_medication_text(ocr_text)

        # Log both original and processed text
        logger.info(f"Original OCR text: {ocr_text}")
        logger.info(f"Processed medication text: {processed_text}")

        # Notify the client about progress (60% complete)
        try:
            await manager.send_progress(job_id, 60)
            await asyncio.sleep(0.01)
        except (WebSocketDisconnect, RuntimeError) as e:
            logger.error(f"Error sending progress update: {e}")

        # Prepare payload for API call
        payload = {
            "products": [
                {
                    "designation": processed_text,
                    "ID": random_id
                }
            ]
        }

        # Log the payload for debugging
        logger.info(f"Payload for TAP API: {payload}")

        # Make API call to associate products
        import requests
        from urllib.parse import urlencode

        # Prepare parameters
        params = {
            'single_result': 'false',
            'origine': 'ocr',
            'plus_associations': 'false',
            'score': '10'
        }

        # API endpoint
        api_url = f"{tapUrl}/tap/associate-products"
        logger.info(f"TAP API URL: {api_url}")

        # Fix the AuthorizationUser header - replace "BearerUser" with "Bearer"
        fixed_auth_user = AuthorizationUser
        if fixed_auth_user.startswith("BearerUser "):
            fixed_auth_user = "Bearer " + fixed_auth_user[11:]  # Replace "BearerUser " with "Bearer "
            logger.info(f"Fixed AuthorizationUser header from 'BearerUser' to 'Bearer'")

        # Prepare headers with authorization tokens - exactly as specified
        headers = {
            'Authorization': fixed_auth_user,
            'AuthorizationTenant': AuthorizationTenant,
            'Content-Type': 'application/json',
        }

        # Log the full headers with complete token values
        # logger.info(f"Full Authorization header: {fixed_auth_user}")
        # logger.info(f"Full AuthorizationTenant header: {AuthorizationTenant}")
        # logger.info(f"Headers for TAP API: {headers}")

        # First attempt
        response = None
        try:
            logger.info("Making first attempt to call TAP API")
            response = requests.post(
                api_url,
                json=payload,
                params=params,
                headers=headers,
                timeout=30  # Adding timeout to prevent hanging requests
            )

            # Check if request was successful
            if response.status_code == 200:
                logger.info("First TAP API call successful")
                results = response.json()
            else:
                logger.warning(
                    f"First TAP API call failed with status code: {response.status_code}. Response: {response.text}. Retrying...")
                raise Exception(f"First attempt failed with status code {response.status_code}")

        except Exception as e:
            logger.warning(f"First TAP API call attempt failed: {str(e)}. Retrying...")

            # Add a short delay before retrying
            await asyncio.sleep(2)

            # Second attempt
            try:
                logger.info("Making second attempt to call TAP API")
                response = requests.post(
                    api_url,
                    json=payload,
                    params=params,
                    headers=headers,
                    timeout=30
                )

                if response.status_code != 200:
                    logger.error(f"Second TAP API call also failed with status code: {response.status_code}")
                    logger.error(f"Response: {response.text}")
                    raise HTTPException(status_code=response.status_code,
                                        detail=f"API request failed after retry: {response.text}")
                else:
                    logger.info("Second TAP API call successful")
                    results = response.json()
            except Exception as retry_error:
                logger.error(f"Second TAP API call attempt failed: {str(retry_error)}")
                raise HTTPException(status_code=500,
                                    detail=f"API request failed after retry: {str(retry_error)}")

        # Notify the client about progress (100% complete)
        try:
            await manager.send_progress(job_id, 100)
            await asyncio.sleep(0.01)
        except (WebSocketDisconnect, RuntimeError) as e:
            logger.error(f"Error sending progress update: {e}")

        return results

    except HTTPException as http_exc:
        # Re-raise HTTP exceptions
        raise http_exc
    except Exception as e:
        logger.error(f"Error in get_info_medicament_and_suggestions_process: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))



async def save_image_async_medic(output_dir_path, random_id, image):
    """Handle Image"""
    # Save the uploaded image file to the temporary directory
    image_name = 'image_medicament_origin_' + random_id + '.jpg'
    image_path = output_dir_path / image_name

    async with aiofiles.open(image_path, 'wb') as out_file:
        content = await image.read()
        await out_file.write(content)

    # Ensure the image file was saved
    if not image_path.exists():
        raise HTTPException(status_code=404, detail="Image file could not be saved.")

    return str(image_path)
