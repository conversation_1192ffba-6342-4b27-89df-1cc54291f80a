############################################
# deeplab_service.py
############################################
import os
import gc
import cv2
import numpy as np
import torch
import torchvision.transforms as torchvision_T
from torchvision.models.segmentation import deeplabv3_resnet50, deeplabv3_mobilenet_v3_large

###########################
# 1) MODEL LOADING
###########################

def get_project_root():
    """Finds the root directory of the project by moving up from the current script's location."""
    current_dir = os.path.abspath(__file__)  # Path to this script
    while current_dir and not os.path.exists(os.path.join(current_dir, "pre-trained-models")):
        new_dir = os.path.dirname(current_dir)
        if new_dir == current_dir:
            raise RuntimeError("Project root with 'pre-trained-models' folder not found.")
        current_dir = new_dir
    return current_dir


def load_deeplab_model(
    model_name: str = "mbv3",
    device: torch.device = torch.device("cpu"),
    num_classes: int = 2
):
    """
    Loads a Deeplabv3 model (mobilenet_v3_large or resnet50) with given 'model_name'.
    Expects checkpoint files in the current working directory.
    Returns the model ready for inference.
    """

    root_dir = get_project_root()
    model_path = os.path.join(root_dir, "pre-trained-models")

    if model_name == "mbv3":
        model = deeplabv3_mobilenet_v3_large(num_classes=num_classes, aux_loss=True)
        checkpoint_path = os.path.join(model_path, "model_mbv3_iou_mix_2C049.pth")
    else:
        model = deeplabv3_resnet50(num_classes=num_classes, aux_loss=True)
        checkpoint_path = os.path.join(model_path, "model_r50_iou_mix_2C020.pth")

    if not os.path.exists(checkpoint_path):
        raise FileNotFoundError(f"Model file not found at: {checkpoint_path}")

    model.to(device)

    # NOTE: If your .pth file is purely weights, "weights_only=True" is fine.
    # If you have trouble, remove that param:
    checkpoints = torch.load(checkpoint_path, map_location=device, weights_only=True)
    model.load_state_dict(checkpoints, strict=False)
    model.eval()

    # Warm up:
    _ = model(torch.randn((1, 3, 384, 384), device=device))
    return model

###########################
# 2) PREPROCESS
###########################
_mean = (0.4611, 0.4359, 0.3905)
_std  = (0.2193, 0.2150, 0.2109)

_deep_transforms = torchvision_T.Compose([
    torchvision_T.ToTensor(),
    torchvision_T.Normalize(_mean, _std),
])

###########################
# 3) CORE FUNCTION: deep_scan_cv2
###########################
def deep_scan_cv2(
    image: np.ndarray,
    trained_model: torch.nn.Module,
    image_size: int = 384,
    device: torch.device = torch.device("cpu"),
) -> tuple:
    """
    Performs document segmentation using a pre-trained Deeplab model.
    Returns:
        (final_cropped_image, corners_4)
    where corners_4 is a (4,2) array of corners in the original image coordinate space.
    If detection fails => (None, None).
    """

    if image is None:
        return (None, None)

    imH, imW = image.shape[:2]

    # 1) Resize to model dimension
    image_resized = cv2.resize(image, (image_size, image_size), interpolation=cv2.INTER_NEAREST)

    # 2) Torch transforms -> move to device
    tensor_im = _deep_transforms(image_resized).unsqueeze(0).to(device)

    with torch.no_grad():
        out = trained_model(tensor_im)["out"]  # shape => [1, num_classes, H, W]
        out = out.cpu()

    del tensor_im
    gc.collect()

    # Argmax => segmentation mask
    mask = torch.argmax(out, dim=1)[0].numpy().astype(np.uint8) * 255

    # Pad to avoid boundary issues
    half = image_size // 2
    padded = np.zeros((image_size + mask.shape[0], image_size + mask.shape[1]), dtype=mask.dtype)
    padded[half : half + image_size, half : half + image_size] = mask

    canny = cv2.Canny(padded, 225, 255)
    canny = cv2.dilate(canny, cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5)))
    contours, _ = cv2.findContours(canny, cv2.RETR_LIST, cv2.CHAIN_APPROX_NONE)

    if not contours:
        return (None, None)

    # Largest contour => approximate corners
    page = max(contours, key=cv2.contourArea)
    epsilon = 0.02 * cv2.arcLength(page, True)
    corners = cv2.approxPolyDP(page, epsilon, True).astype(np.float32)

    # Shift back
    corners[:, 0, 0] -= half
    corners[:, 0, 1] -= half

    # Scale back to original
    scale_x = imW / float(image_size)
    scale_y = imH / float(image_size)
    corners[:, 0, 0] *= scale_x
    corners[:, 0, 1] *= scale_y

    if len(corners) < 4:
        return (None, None)

    corners_4 = corners.reshape(-1, 2)
    corners_4 = _order_points(corners_4)

    # Compute final warp
    destination = _find_dest(corners_4)
    M = cv2.getPerspectiveTransform(np.float32(corners_4), np.float32(destination))
    maxW, maxH = int(destination[2][0]), int(destination[2][1])

    if maxW < 1 or maxH < 1:
        return (None, None)

    final = cv2.warpPerspective(image.astype(np.float32), M, (maxW, maxH), flags=cv2.INTER_LANCZOS4)
    final = np.clip(final, 0, 255).astype(np.uint8)

    return (final, corners_4)

###########################
# 4) HELPER FUNCTIONS
###########################
def _order_points(pts):
    """Return corners in [top-left, top-right, bottom-right, bottom-left]."""
    rect = np.zeros((4, 2), dtype="float32")
    s = pts.sum(axis=1)
    rect[0] = pts[np.argmin(s)]   # top-left
    rect[2] = pts[np.argmax(s)]   # bottom-right
    diff = np.diff(pts, axis=1)
    rect[1] = pts[np.argmin(diff)] # top-right
    rect[3] = pts[np.argmax(diff)] # bottom-left
    return rect

def _find_dest(pts):
    """
    Given 4 corners in [tl, tr, br, bl],
    returns standard rectangular destination of [0,0], [w,0], [w,h], [0,h].
    """
    (tl, tr, br, bl) = pts
    widthA = np.sqrt(((br[0] - bl[0]) ** 2) + ((br[1] - bl[1]) ** 2))
    widthB = np.sqrt(((tr[0] - tl[0]) ** 2) + ((tr[1] - tl[1]) ** 2))
    maxWidth = max(int(widthA), int(widthB))

    heightA = np.sqrt(((tr[0] - br[0]) ** 2) + ((tr[1] - br[1]) ** 2))
    heightB = np.sqrt(((tl[0] - bl[0]) ** 2) + ((tl[1] - bl[1]) ** 2))
    maxHeight = max(int(heightA), int(heightB))

    return np.array([
        [0,         0],
        [maxWidth,  0],
        [maxWidth,  maxHeight],
        [0,         maxHeight]
    ], dtype="float32")
