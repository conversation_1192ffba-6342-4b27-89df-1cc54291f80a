import jwt
from datetime import datetime, timedelta
from jwt.exceptions import ExpiredSignatureError, InvalidTokenError
from fastapi import HTTPException
import base64

from src.app.config import SECRET_KEY, ALGORITHM

# Constants for secret keys
USER_SECRET = "0/Q4is+oA7xjhkPr4tNgLF9V7F35aqubzCi10AbsWxxRtlWKTUL67YCMXIbJQAk765aD3qZkHrVQpR/ZQnFwoAvg7fR7jqrvqRAaF9Dh+UE="
TENANT_SECRET = "L/2qcslSwpQcZGYkMKiqlHo8Su76BP4bbQ25gBArbjhRIEf49ZjawBAP/g0Ip5RwNiEv10YyqPQCdetxSSt6E0bBX4yMNZhlnvi1qXfRXDw="


def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token has expired")
    except InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")


def verify_token_winplus(token: str, is_tenant: bool = False):
    try:
        secret = base64.b64decode(TENANT_SECRET if is_tenant else USER_SECRET)
        payload = jwt.decode(token, secret, algorithms=["HS512"])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token has expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")
