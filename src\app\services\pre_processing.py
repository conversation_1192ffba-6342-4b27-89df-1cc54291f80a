import numpy as np
import cv2
from matplotlib import pyplot as plt
import os
import pytesseract as tess
from pathlib import Path
from PIL import Image, ImageDraw
from src.app.camscanner_lite.pyimagesearch import imutils
from src.app.camscanner_lite.pyimagesearch import transform

from src.app.utils import constants
from src.app.config import TESSERACT_PATH
import traceback
import logging

# from reportlab.lib.pagesizes import letter
# from reportlab.pdfgen import canvas

# Set the path to the Tesseract executable
tess.pytesseract.tesseract_cmd = TESSERACT_PATH

"""-----********************************** Crop the Image *********************************** -----"""


def detect_and_remove_outside_contours(image_path):
    """Detect document contours and remove all parts of the image that are outside the detected contours."""
    try:
        # Load the image
        image = cv2.imread(image_path)

        if image is None:
            logging.error(f"Error: Image could not be read at {image_path}.")
            raise FileNotFoundError(f"Fichier image introuvable à l'adresse: {image_path}")

        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Threshold to get binary image
        # _, binary = cv2.threshold(gray, 180, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        # binary = cv2.adaptiveThreshold(
        #     gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        #     cv2.THRESH_BINARY, 11, 15
        # )
        _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_TRUNC)

        # Find contours
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Find the largest contour which should be the document
        largest_contour = max(contours, key=cv2.contourArea)
        cv2.drawContours(image, contours, -1, (0, 255, 0), 3)
        # Create a mask with the same dimensions as the image
        mask = np.zeros_like(gray)

        # Fill the detected contour on the mask
        cv2.drawContours(mask, [largest_contour], -1, (255), thickness=cv2.FILLED)

        # Apply the mask to the image
        result = cv2.bitwise_and(image, image, mask=mask)

        # # Display the binary, original, and masked images
        # plt.figure(figsize=(18, 6))
        # plt.subplot(1, 3, 1)
        # plt.title('Binary Image')
        # plt.imshow(binary, cmap='gray')
        # plt.axis('off')
        #
        # plt.subplot(1, 3, 2)
        # plt.title('Original Image')
        # plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        # plt.axis('off')
        #
        # plt.subplot(1, 3, 3)
        # plt.title('Image After Removing Outside Contours')
        # plt.imshow(cv2.cvtColor(result, cv2.COLOR_BGR2RGB))
        # plt.axis('off')
        #
        # plt.show()

        return result

    except Exception as e:
        logging.error(
            f"Une erreur est survenue dans la méthode detect_and_remove_outside_contours: {traceback.format_exc()}")
        raise e

def preprocess_image(image):
    """Pre-process the image to normalize lighting and enhance contrast."""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Apply Gaussian blur to reduce noise
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # Adaptive histogram equalization
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    equalized = clahe.apply(blurred)

    # Apply adaptive thresholding
    adaptive_thresh = cv2.adaptiveThreshold(equalized, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                            cv2.THRESH_BINARY_INV, 11, 2)

    return adaptive_thresh


def process_image_crop(image_path, output_dir_path_param, model, random_id):
    """Process an image to extract the header and table parts with pre-processing."""
    try:
        from pathlib import Path
        import cv2
        import numpy as np
        import logging
        import traceback

        # Ensure the output directory exists
        output_dir_path = Path(output_dir_path_param)
        output_dir_path.mkdir(parents=True, exist_ok=True)

        # Initialize variables to avoid UnboundLocalError
        header_image_path = None
        table_image_path = None

        def preprocess_image(image):
            """Pre-process the image to normalize lighting and enhance contrast."""
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)

            # Adaptive histogram equalization
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            equalized = clahe.apply(blurred)

            # Apply adaptive thresholding
            adaptive_thresh = cv2.adaptiveThreshold(equalized, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                                    cv2.THRESH_BINARY_INV, 11, 2)
            return adaptive_thresh

        # Read and preprocess the image
        image = cv2.imread(str(image_path))
        if image is None:
            logging.error(f"Error: Image at {image_path} could not be read.")
            raise FileNotFoundError(f"Fichier image introuvable à l'adresse: {image_path}")

        preprocessed_image = preprocess_image(image)

        # Save the preprocessed image for debugging
        cv2.imwrite(str(output_dir_path / f"Preprocessed_{random_id}_{model}.jpg"), preprocessed_image)

        # Dilate to find contours
        kernel = np.ones((1, 1), np.uint8)
        dilated = cv2.dilate(preprocessed_image, kernel, iterations=1)
        contours, _ = cv2.findContours(dilated.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            logging.error("No contours detected. Image might be too light or pre-processing failed.")
            return None, None

        # Find the largest contour
        largest_area = 0
        largest_contour = None
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            area = w * h
            if area > largest_area:
                largest_area = area
                largest_contour = contour


        footer_height = constants.footer_heights.get(model, 400)  # Default footer height


        # Save the header, table, and footer images
        if largest_contour is not None:
            x, y, w, h = cv2.boundingRect(largest_contour)

            # Crop the header image
            header_image = image[0:y, 0:image.shape[1]]
            if header_image.size > 0:
                header_image_path = output_dir_path / f"BL_header_cropped_{random_id}_{model}__.jpg"
                cv2.imwrite(str(header_image_path), header_image)
            else:
                logging.error("Error: Header image is empty.")

            # Crop the table image
            table_image = image[y:y + h, x:x + w + constants.additional_table_client.get(model, 0)]
            if table_image.size > 0:
                table_image_path = output_dir_path / f"BL_table_cropped_{random_id}_{model}__.jpg"
                cv2.imwrite(str(table_image_path), table_image)
            else:
                logging.error("Error: Table image is empty.")

            # Crop the footer image
            footer_image = image[image.shape[0] - footer_height:image.shape[0], 0:image.shape[1]]
            if footer_image.size > 0:
                footer_image_path = output_dir_path / f"BL_footer_cropped_{random_id}_{model}__.jpg"
                cv2.imwrite(str(footer_image_path), footer_image)
            else:
                logging.error("Error: Footer image is empty.")

        # Return the paths, even if some images are None
        return header_image_path, table_image_path

    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode process_image_crop: {traceback.format_exc()}")
        raise e

# Function to hide columns in an image
def hide_columns(image_path, columns_to_hide):
    """
    Hide specific columns in the image by drawing a white rectangle over them.

    :param image_path: The path to the image file.
    :param columns_to_hide: A list of tuples, where each tuple contains the coordinates
                            (left, upper, right, lower) of the rectangle to draw over a column.
    :return: An image object with specified columns hidden.
    """
    # Open the pre-processed image
    """Hide specific columns in the image by drawing a white rectangle over them."""
    try:
        image = Image.open(image_path)

        # Ensure the image is in RGB mode for color operations
        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Create a drawing object
        draw = ImageDraw.Draw(image)

        # Set the fill color to white (or the background color of the document)
        fill_color = (255, 255, 255)

        # Draw a rectangle over each column to hide it
        for column in columns_to_hide:
            draw.rectangle(column, fill=fill_color)

        # Return the modified image
        return image

    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode hide_columns: {traceback.format_exc()}")
        raise e


# Function to hide contours of table  in an image
def remove_countours_table(image_path, image):
    """Remove contours of a table in an image."""
    try:
        image = cv2.imread(image_path) if image_path is not None else image
        result = image.copy()
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)[1]

        # Remove horizontal lines
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
        remove_horizontal = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, horizontal_kernel, iterations=2)
        cnts = cv2.findContours(remove_horizontal, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cnts = cnts[0] if len(cnts) == 2 else cnts[1]
        for c in cnts:
            cv2.drawContours(result, [c], -1, (255, 255, 255), 5)

        # Remove vertical lines
        vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))
        remove_vertical = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, vertical_kernel, iterations=2)
        cnts = cv2.findContours(remove_vertical, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cnts = cnts[0] if len(cnts) == 2 else cnts[1]
        for c in cnts:
            cv2.drawContours(result, [c], -1, (255, 255, 255), 5)

        return result

    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode remove_countours_table: {traceback.format_exc()}")
        raise e


"""-----**************************** Pre Processing the Image ******************************** -----"""


def display(im_path):
    dpi = 80
    im_data = plt.imread(im_path)

    height, width = im_data.shape[:2]

    # What size does the figure need to be in inches to fit the image?
    figsize = width / float(dpi), height / float(dpi)

    # Create a figure of the right size with one axes that takes up the full figure
    fig = plt.figure(figsize=figsize)
    ax = fig.add_axes([1, 1, 0, 0])

    # Hide spines, ticks, etc.
    ax.axis('off')

    # Display the image.
    ax.imshow(im_data, cmap='gray')

    plt.show()


def deskew(image):
    try:
        # Find coordinates of non-zero pixels
        co_ords = np.where(image > 0)

        # Convert the row and column indices to a two-dimensional array
        co_ords = np.column_stack((co_ords[1], co_ords[0])).astype(np.float32)

        # Calculate the angle of rotation
        angle = cv2.minAreaRect(co_ords)[-1]

        if angle < -45:
            angle = -(90 + angle)
        else:
            angle = -angle

        # Get image dimensions
        (h, w) = image.shape[:2]

        # Calculate the center point
        center = (w // 2, h // 2)

        # Generate rotation matrix
        M = cv2.getRotationMatrix2D(center, angle, 1.0)

        # Apply rotation to the image
        rotated = cv2.warpAffine(image, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)

        return rotated

    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode deskew: {traceback.format_exc()}")
        raise e


def noise_removal(image):
    """Remove noise from the image."""
    try:
        kernel = np.ones((1, 1), np.uint8)
        image = cv2.dilate(image, kernel, iterations=1)
        kernel = np.ones((1, 1), np.uint8)
        image = cv2.erode(image, kernel, iterations=1)
        image = cv2.morphologyEx(image, cv2.MORPH_CLOSE, kernel)
        image = cv2.medianBlur(image, 3)
        return image
    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode noise_removal: {traceback.format_exc()}")
        raise e


def thin_font(image, model, part):
    """Apply thinning to the font in the image."""
    try:
        image = cv2.bitwise_not(image)
        kernel_sizes = constants.kernel_sizes_thin_font
        kernel = np.ones(kernel_sizes.get(model).get(part), np.uint8)
        image = cv2.erode(image, kernel, iterations=1)
        image = cv2.bitwise_not(image)
        return image
    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode thin_font: {traceback.format_exc()}")
        raise e


def thick_font(image, model, part):
    """Apply thickening to the font in the image."""
    try:
        image = cv2.bitwise_not(image)
        kernel_sizes = constants.kernel_sizes_thick_font
        kernel = np.ones(kernel_sizes.get(model).get(part), np.uint8)
        image = cv2.dilate(image, kernel, iterations=1)
        image = cv2.bitwise_not(image)
        return image
    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode thick_font: {traceback.format_exc()}")
        raise e


def processing_the_img_old(image, model, part):
    try:
        # if model == constants.SOPHACA:
        #     image = remove_countours_table(None, image.copy())

        # -- - Skew Correction
        binary_image = None
        skew_correction = deskew(image)

        # 1 - Inverted Images
        inverted_image = cv2.bitwise_not(image)

        # 2 - Binarization - Convert the image to grayscale
        gray_image = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)  # cv2.COLOR_BGR2GRAY || cv2.COLOR_RGB2GRAY

        # Apply Gaussian blur to remove noise
        blurred_image = cv2.GaussianBlur(gray_image, (5, 5), 0)

        # 3 - Threshold - Apply thresholding to create a binary image
        # Define threshold settings for different models
        threshold_settings = constants.threshold_settings

        # logging.info("threshold_settings : ", threshold_settings.get[model][part])

        # Get the threshold settings for the specified model
        # settings = threshold_settings.get[model][part]
        # settings = threshold_settings.get(model).get(part)
        settings_model = threshold_settings.get(model)

        if settings_model is None:
            raise ValueError(f"Model '{model}' not found in threshold settings.")

        settings = settings_model.get(part)
        if settings is None:
            raise ValueError(f"Part '{part}' not found for model '{model}' in threshold settings.")

        # Apply the appropriate thresholding method
        if settings['type'] == 'otsu':
            _, binary_image = cv2.threshold(blurred_image, settings['value'], settings['max_value'],
                                            cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        elif settings['type'] == 'binary':
            _, binary_image = cv2.threshold(gray_image, settings['value'], settings['max_value'],
                                            cv2.THRESH_BINARY)
        elif settings['type'] == 'tozero':
            _, binary_image = cv2.threshold(gray_image, settings['value'], settings['max_value'],
                                            cv2.THRESH_TOZERO)
        elif settings['type'] == 'adaptive':
            binary_image = cv2.adaptiveThreshold(
                gray_image, settings['max_value'], settings['adaptive_method'],
                settings['threshold_type'], settings['block_size'], settings['C']
            )

        # 4 - Noise Removal
        no_noise = noise_removal(binary_image)

        # 5 - Dilation and Erosion
        eroded_image = thin_font(no_noise, model, part)

        # 6 - Thick Font
        dilated_image = thick_font(eroded_image, model, part)

        # 7 - Removing Shadows
        rgb_planes = cv2.split(dilated_image)
        result_planes = []
        result_norm_planes = []
        for plane in rgb_planes:
            dilated_img = cv2.dilate(plane, np.ones((7, 7), np.uint8))
            bg_img = cv2.medianBlur(dilated_img, 21)
            diff_img = 255 - cv2.absdiff(plane, bg_img)
            result_planes.append(diff_img)
        img = cv2.merge(result_planes)

        eroded_image = thin_font(img, model, part)

        return eroded_image

    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode processing_the_img: {traceback.format_exc()}")
        raise e



def processing_the_img(image_path, model, part):
    try:
        # Check if image_path is already a numpy array (image object)
        if isinstance(image_path, np.ndarray):
            image = image_path
        else:
            # Load the image if a path is provided
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not read image from path: {image_path}")

        RESCALED_HEIGHT = 500.0

        # Define threshold settings for different models
        threshold_settings = constants.threshold_settings
        settings_model = threshold_settings.get(model)



        # Compute ratio and resize
        ratio = image.shape[0] / RESCALED_HEIGHT
        orig = image.copy()
        rescaled_image = imutils.resize(image, height=int(RESCALED_HEIGHT))

        # Convert to grayscale
        gray = cv2.cvtColor(rescaled_image, cv2.COLOR_BGR2GRAY)

        # Sharpen image
        sharpen = cv2.GaussianBlur(gray, (0, 0), 3)
        sharpen = cv2.addWeighted(gray, 1.5, sharpen, -0.5, 0)

        # # Apply adaptive threshold

        if settings_model is None:
            raise ValueError(f"Model '{model}' not found in threshold settings.")

        settings = settings_model.get(part)
        if settings is None:
            raise ValueError(f"Part '{part}' not found for model '{model}' in threshold settings.")

        # Apply the appropriate thresholding method
        if settings['type'] == 'otsu':
            _, thresh = cv2.threshold(sharpen, settings['value'], settings['max_value'],
                                            cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        elif settings['type'] == 'binary':
            _, thresh = cv2.threshold(sharpen, settings['value'], settings['max_value'],
                                            cv2.THRESH_BINARY)
        elif settings['type'] == 'tozero':
            _, thresh = cv2.threshold(sharpen, settings['value'], settings['max_value'],
                                            cv2.THRESH_TOZERO)
        elif settings['type'] == 'adaptive':
            thresh = cv2.adaptiveThreshold(
                sharpen, settings['max_value'], settings['adaptive_method'],
                settings['threshold_type'], settings['block_size'], settings['C']
            )

        try:
            thresh = cv2.bitwise_not(thresh)

            kernel_sizes = constants.kernel_sizes_thick_font
            kernel = np.ones(kernel_sizes.get(model).get(part), np.uint8)
            thresh = cv2.dilate(thresh, kernel, iterations=1)
            thresh = cv2.bitwise_not(thresh)
        except Exception as e:
            logging.error(f"Une erreur est survenue dans la méthode thin_font: {traceback.format_exc()}")
            raise e

        try:
            thresh = cv2.bitwise_not(thresh)
            kernel_sizes = constants.kernel_sizes_thin_font
            kernel = np.ones(kernel_sizes.get(model).get(part), np.uint8)
            thresh = cv2.erode(thresh, kernel, iterations=1)
            thresh = cv2.bitwise_not(thresh)
        except Exception as e:
            logging.error(f"Une erreur est survenue dans la méthode thin_font: {traceback.format_exc()}")
            raise e


        return thresh

    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode processing_the_img: {traceback.format_exc()}")
        raise e


def apply_preprocessing(output_dir_path, model, random_id):
    try:
        output_dir_path = Path(output_dir_path)
        output_dir_path.mkdir(parents=True, exist_ok=True)  # Ensure directory exists.

        # Loop through each image in the sub-folder
        for image_name in os.listdir(output_dir_path):

            split_char = '.'
            # Define the list of file extensions
            # if (model == constants.SOPHADIMS or model == constants.SOPHACA or model == constants.GLOBAL) and 'table' in image_name.lower():
            if (model == constants.GLOBAL) and 'table' in image_name.lower():
                # **** Processing After remove Contours ****
                list_c_extensions = ('_cropped_' + random_id + '_' + model + '_rmv_c.png',
                                     '_cropped_' + random_id + '_' + model + '_rmv_c.jpg',
                                     '_cropped_' + random_id + '_' + model + '_rmv_c.jpeg')

            else:
                # **** Processing Before remove Contours ****
                list_c_extensions = ('_cropped_' + random_id + '_' + model + '__.png',
                                     '_cropped_' + random_id + '_' + model + '__.jpg',
                                     '_cropped_' + random_id + '_' + model + '__.jpeg')

                split_char = '__.'

            # **** Processing Before remove Contours ****
            if image_name.lower().endswith(tuple(ext.lower() for ext in list_c_extensions)):
            # if image_name.lower().endswith(list_c_extensions):
                image_path = os.path.join(output_dir_path, image_name)
                image = cv2.imread(image_path)

                # Determine the part of the image from the filename
                part = 'table'  # Default to 'table'
                if 'header' in image_name.lower():
                    part = 'header'
                elif 'footer' in image_name.lower():
                    part = 'footer'

                # Apply the preprocessing functions to the image
                preprocessed_image = processing_the_img(image, model, part)

                # Save the processed image in the new folder with the modified name
                processed_image_path = os.path.join(output_dir_path, image_name.split(split_char)[0] + '_processed.jpg')
                cv2.imwrite(processed_image_path, preprocessed_image)

                # Show the processed image
                # plt.imshow(cv2.cvtColor(preprocessed_image, cv2.COLOR_BGR2RGB))
                # plt.show()

    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode apply_preprocessing: {traceback.format_exc()}")
        raise e


def apply_remove_contours_table(output_dir_path, model, random_id):
    """Remove contours from the table images."""
    try:
        # if model != constants.SOPHADIMS and model != constants.SOPHACA and model != constants.GLOBAL:
        if model != constants.GLOBAL:
            # Apply the processing image before hide Columns **** Processing Before remove Contours ****
            apply_preprocessing(output_dir_path, model, random_id)

        # Loop through each image in the sub-folder
        for image_name in os.listdir(output_dir_path):

            # Define the list of file extensions
            # if model == constants.SOPHADIMS or model == constants.SOPHACA or model == constants.GLOBAL:
            if model == constants.GLOBAL:
                # **** Processing After remove Contours ****
                list_c_extensions = ('_table_cropped_' + random_id + '_' + model + '__.png',
                                     '_table_cropped_' + random_id + '_' + model + '__.jpg',
                                     '_table_cropped_' + random_id + '_' + model + '__.jpeg')

                split_char = '__.'
            else:
                # **** Processing Before remove Contours ****
                list_c_extensions = ('_table_cropped_' + random_id + '_' + model + '_processed.png',
                                     '_table_cropped_' + random_id + '_' + model + '_processed.jpg',
                                     '_table_cropped_' + random_id + '_' + model + '_processed.jpeg')

                split_char = '.'

            # **** Processing Before remove Contours ****
            # if image_name.lower().endswith(list_c_extensions):
            if image_name.lower().endswith(tuple(ext.lower() for ext in list_c_extensions)):
                image_path = os.path.join(output_dir_path, image_name)

                # Apply the preprocessing functions to the image
                edited_image = remove_countours_table(image_path, None)
                # edited_image = cv2.imread(image_path)

                # Save the processed image with the modified name

                edited_image_path = os.path.join(output_dir_path, image_name.split(split_char)[0] + '_rmv_c.jpg')
                cv2.imwrite(edited_image_path, edited_image)

                # Show the processed image
                # plt.imshow(cv2.cvtColor(edited_image, cv2.COLOR_BGR2RGB))
                # plt.show()
        # if model == constants.SOPHADIMS or model == constants.SOPHACA or model == constants.GLOBAL:
        if model == constants.GLOBAL:
            # Apply the processing image after hide Columns **** Processing Before remove Contours ****
            apply_preprocessing(output_dir_path, model, random_id)

    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode apply_remove_contours_table: {traceback.format_exc()}")
        raise e


def resize_table_to_standard(image_path, model):
    """
    Resizes the table image for `model` to a standard width & height,
    so columns to hide remain consistent across different invoice sizes.
    Returns the resized image (as a PIL Image or NumPy array).
    """
    import cv2

    # Read image with OpenCV
    img = cv2.imread(image_path)
    if img is None:
        raise FileNotFoundError(f"Could not open image for resizing: {image_path}")

    # If the model has a known standard size, do the resize
    from src.app.utils.constants import STANDARD_TABLE_SIZE
    if model in STANDARD_TABLE_SIZE:
        (target_w, target_h) = STANDARD_TABLE_SIZE[model]

        # Resize (note that OpenCV uses (width, height) in reverse for shape)
        resized = cv2.resize(img, (target_w, target_h), interpolation=cv2.INTER_AREA)
        return resized
    else:
        # If no standard size is defined for this model, we can skip resizing
        return img

def hide_columns_pil(pil_image, columns_to_hide):
    """
    Hide columns in a PIL image object by drawing white rectangles.
    Returns the modified PIL image.
    """
    # Ensure the image is in RGB
    if pil_image.mode != 'RGB':
        pil_image = pil_image.convert('RGB')
    draw = ImageDraw.Draw(pil_image)
    fill_color = (255, 255, 255)

    for column_rect in columns_to_hide:
        draw.rectangle(column_rect, fill=fill_color)

    return pil_image

def apply_hide_columns(output_dir_path, model, random_id):
    """Remove contours from the table images."""
    try:
        # Apply the processing image before hide Columns
        suffix = ''
        if model == constants.GPM or model == constants.SOPHADIMS or model == constants.SPR or model == constants.SOPHACA or model == constants.GLOBAL:  # or model == constants.RECAMED:
        # if model == constants.GPM or model == constants.SOPHACA or model == constants.GLOBAL:
            apply_remove_contours_table(output_dir_path, model, random_id)
            if model == constants.GLOBAL:
                file_name_dir = 'table_cropped_' + random_id + '_' + model + '_rmv_c_processed.jpg'
            else:
                file_name_dir = 'table_cropped_' + random_id + '_' + model + '_processed_rmv_c.jpg'
        else:
            # --  For COOPER_PHARMA_CASA
            apply_preprocessing(output_dir_path, model, random_id)
            file_name_dir = 'table_cropped_' + random_id + '_' + model + '_processed.jpg'

        # Get settings for the specified model
        settings = constants.model_settings.get(model, constants.model_settings[
            'GLOBAL'])  # Default to 'GLOBAL' if model not found

        # Loop through each image in the sub-folder
        for image_name in os.listdir(output_dir_path):
            if file_name_dir.lower() in image_name.lower():
                image_path = os.path.join(output_dir_path, image_name)

                # 1) Resize image to standard dimension first
                resized_np = resize_table_to_standard(image_path, model)

                # 2) Convert from NumPy to PIL so we can call hide_columns
                from PIL import Image
                resized_pil = Image.fromarray(cv2.cvtColor(resized_np, cv2.COLOR_BGR2RGB))

                # 3) Now apply hide_columns on the PIL image
                edited_image_pil = hide_columns_pil(resized_pil, settings['columns'])

                # 4) Convert result back to NumPy for saving
                edited_image_np = cv2.cvtColor(np.array(edited_image_pil), cv2.COLOR_RGB2BGR)

                # 5) Save final
                final_bl_table_name = f"BL_table_final_format_{random_id}_{model}.jpg"
                edited_image_path = os.path.join(output_dir_path, final_bl_table_name)
                cv2.imwrite(edited_image_path, edited_image_np)

                # (Optional) Save as PDF
                edited_image_pdf_path = edited_image_path.replace('.jpg', '.pdf')
                edited_image_pil.save(edited_image_pdf_path, 'PDF', resolution=100.0)

                # # Save as PDF with reportlab
                # edited_image_pdf_path = os.path.join(output_dir_path, final_bl_table_name.replace('.jpg', '.pdf'))
                # # Ensure the image is in RGB mode
                # if edited_image.mode != 'RGB':
                #     edited_image = edited_image.convert('RGB')
                # # Get the dimensions of the image
                # img_width, img_height = edited_image.size
                # # Create a canvas with the image size
                # c = canvas.Canvas(edited_image_pdf_path, pagesize=(img_width, img_height))
                # # Draw the image onto the canvas
                # c.drawImage(ImageReader(edited_image), 0, 0, width=img_width, height=img_height)
                # # Save the PDF file
                # c.showPage()
                # c.save()

                # Show the processed image
                # plt.imshow(cv2.cvtColor(edited_image_np, cv2.COLOR_BGR2RGB))
                # plt.show()

    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode apply_remove_contours_table: {traceback.format_exc()}")
        raise e




"""-----************************************* Testing ******************************************** -----"""

# output_dir_path = Path("../data/BL_Scanned_SOPHACA/tables")
# apply_hide_columns(output_dir_path)

# image_file = "../data/origin_BL/BLS_2.jpg"
# output_dir_path = Path("../data/output_preprocessing")
# # process_image_crop(image_file, output_dir_path, constants.RECAMED)
# apply_hide_columns(output_dir_path, constants.RECAMED)
