Structure Porjet
ocr_document_grossiste/
│
├── data/
├── original_json/
├── original_txt/
├── output_after_corr_txt/
├── output_before_corr_txt/
├── output_json/
├── src/
│   ├── __init__.py
│   ├── __main__.py
│   ├── api.py
│   ├── mindee_config.json
│   ├── database/
│   │   │   ├── ocr_document_grossiste.db
│   ├── app/
│   │   │   ├── __init__.py
│   │   │   ├── app.py
│   │   │   ├── config.py
│   │   │   ├── dependencies.py
│   │   │   ├── init_db.py
│   │   ├── camscanner_lite/
│   │   │   ├── __init__.py
│   │   │   ├── smart_crop.py
│   │   │   ├── deeplab_service.py
│   │   │   ├── ...
│   │   ├── ocr/
│   │   │   ├── __init__.py
│   │   │   ├── header.py
│   │   │   ├── footer.py
│   │   │   ├── table.py
│   │   ├── routes/
│   │   │   ├── __init__.py
│   │   │   ├── identify_supplier.py
│   │   │   ├── magic_pro_filter.py
│   │   │   ├── process_image_supp.py
│   │   │   ├── process_ocr_multi.py
│   │   │   ├── smart_crop.py
│   │   │   ├── login.py
│   │   │   ├── logout.py
│   │   │   ├── suppliers.py
│   │   │   ├── smart_crop.py
│   │   │   ├── ...
│   │   ├── services/
│   │   │   ├── __init__.py
│   │   │   ├── identify_supplier.py
│   │   │   ├── pre_processing.py
│   │   │   ├── websocket_manager.py
│   │   │   ├── ocr_service.py
│   │   ├── utils/
│   │   │   ├── __init__.py
│   │   │   ├── cleanup_script.py
│   │   │   ├── constants.py
│   │   │   ├── document_model.py
│   │   │   ├── helpers.py
│   │   │   ├── models.py
│   │   │   ├── db_operations.py
│   │   │   ├── jwt_utils.py
│   │   │   ├── models_utils.py
│   │   │   ├── expected_results_unitTest.py
│   │   │   ├── ...
│   │   ├── tests/
│   │   │   ├── __init__.py
│   │   │   ├── test_ocr_multiple.py
├── temp/
│   ├── columns/
│   ├── compare_images/
│   ├── identify_suppliers/
│   ├── images_bl_orginal_format/
│   ├── magic_pro_filter_output/
│   ├── origin_BL/
│   ├── origin_images_output/
│   ├── output_preprocessing/
│   └── smart_crop_output/
├── utils/
│   ├── constants.py
│   ├── ...
├── .env.local
├── .env.prod
├── Dockerfile
├── Dockerfile.base
├── Jenkinsfile
├── Jenkinsfile.base
