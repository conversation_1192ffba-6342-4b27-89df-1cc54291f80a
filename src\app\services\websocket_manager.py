from fastapi import <PERSON><PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
import asyncio
from starlette.websockets import WebSocketState
from typing import Dict
import logging

router = APIRouter()

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, job_id: str):
        await websocket.accept()
        self.active_connections[job_id] = websocket
        logging.info(f"WebSocket connection established for job_id: {job_id}")

    def disconnect(self, job_id: str):
        if job_id in self.active_connections:
            websocket = self.active_connections.pop(job_id)
            logging.info(f"WebSocket connection closed for job_id: {job_id}")

    async def send_progress(self, job_id: str, progress: int):
        if job_id in self.active_connections:
            connection = self.active_connections[job_id]
            try:
                if connection.application_state == WebSocketState.CONNECTED:
                    await connection.send_json({"progress": progress, "job_id": job_id})
                    logging.info(f"Sent progress {progress}% for job_id: {job_id}")
                else:
                    logging.warning(f"WebSocket state not connected for job_id: {job_id}")
                    self.disconnect(job_id)
            except (WebSocketDisconnect, RuntimeError, OSError) as e:
                self.disconnect(job_id)
                logging.error(f"WebSocket disconnected or error for job_id {job_id}: {e}")


manager = ConnectionManager()


@router.websocket("/ws/{job_id}")
async def websocket_endpoint(websocket: WebSocket, job_id: str):
    await manager.connect(websocket, job_id)
    try:
        while True:
            await asyncio.sleep(1)
    except WebSocketDisconnect:
        manager.disconnect(job_id)
    except Exception as e:
        logging.error(f"Unexpected error in WebSocket: {e}")
        manager.disconnect(job_id)


