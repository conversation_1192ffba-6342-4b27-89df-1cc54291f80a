import os
from doctr.io import DocumentFile
from doctr.models import ocr_predictor
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import torch
import matplotlib
import logging

@dataclass
class OCRResult:
    """Data class to hold OCR results"""
    raw_lines: List[str]
    structured_data: Optional[List[Dict]] = None


class OCRService:
    """Global OCR service for text extraction from images"""

    def __init__(self):
        self.model = ocr_predictor(pretrained=True)

    def calculate_dynamic_threshold(self, line_heights: List[float]) -> float:
        """
        Calculate dynamic row threshold with adjusted parameters for better line merging
        """
        if not line_heights:
            return 0.015  # Default fallback

        sorted_heights = sorted(line_heights)
        q1 = sorted_heights[len(sorted_heights) // 4]
        q3 = sorted_heights[3 * len(sorted_heights) // 4]
        iqr = q3 - q1
        filtered_heights = [h for h in line_heights if q1 - 1.5 * iqr <= h <= q3 + 1.5 * iqr]

        avg_height = sum(filtered_heights) / len(filtered_heights)
        std_dev = (sum((x - avg_height) ** 2 for x in filtered_heights) / len(filtered_heights)) ** 0.5

        return avg_height * 0.75 + std_dev * 0.5

    def extract_text(self, image_path: str, use_row_threshold: bool = False) -> OCRResult:
        """
        Extract text from image with optional row threshold processing
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image not found: {image_path}")

        # Load and process image
        doc = DocumentFile.from_images(image_path)
        result = self.model(doc)

        # Initialize containers
        raw_lines = []
        current_line_entries = []  # Stores tuples of (x_start, text)
        current_line_y = None
        row_threshold = None

        # Calculate row threshold if needed
        if use_row_threshold:
            line_heights = []
            for page in result.pages:
                for block in page.blocks:
                    for line in block.lines:
                        height = line.geometry[1][1] - line.geometry[0][1]
                        if height > 0:
                            line_heights.append(height)
            row_threshold = self.calculate_dynamic_threshold(line_heights)

        # Process OCR results
        for page in result.pages:
            for block in page.blocks:
                for line in block.lines:
                    # Calculate geometry properties
                    y_center = (line.geometry[0][1] + line.geometry[1][1]) / 2
                    x_start = line.geometry[0][0]
                    text = ' '.join(word.value for word in line.words).strip()

                    if use_row_threshold:
                        if current_line_y is None:
                            current_line_y = y_center
                            current_line_entries.append((x_start, text))
                        elif abs(y_center - current_line_y) <= row_threshold:
                            current_line_entries.append((x_start, text))
                        else:
                            # Sort entries by x_start before adding to raw lines
                            sorted_entries = sorted(current_line_entries, key=lambda x: x[0])
                            raw_lines.append(' '.join([entry[1] for entry in sorted_entries]))
                            current_line_entries = [(x_start, text)]
                            current_line_y = y_center
                    else:
                        # Without threshold, each line is processed individually
                        raw_lines.append(text)

        # Add remaining entries if any
        if use_row_threshold and current_line_entries:
            sorted_entries = sorted(current_line_entries, key=lambda x: x[0])
            raw_lines.append(' '.join([entry[1] for entry in sorted_entries]))

        return OCRResult(raw_lines=raw_lines)


# Usage example:
def main():
    print("Starting OCR service...")
    matplotlib.use('TkAgg')  # Set the backend to TkAgg

    # Print PyTorch version and CUDA info first
    logging.info(f"Using PyTorch version: {torch.__version__}")
    logging.info(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        logging.info(f"CUDA device: {torch.cuda.get_device_name()}")

    ocr_service = OCRService()

    try:
        # image_path = "../origin_BL/tables/gpm.jpg"
        image_path = r'C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\OCR_DOCUMENT_GROSSISTE\ocr_document_grossiste\utils\temp_util\origin_BL\BLS_1.jpg'

        # Example 1: Without row threshold
        result1 = ocr_service.extract_text(image_path, use_row_threshold=False)
        logging.info("Without row threshold:")
        for line in result1.raw_lines:
            logging.info(line)

        logging.info("\n" + "=" * 50 + "\n")

        # Example 2: With row threshold
        result2 = ocr_service.extract_text(image_path, use_row_threshold=True)
        logging.info("With row threshold:")
        for line in result2.raw_lines:
            logging.info(line)

    except Exception as e:
        logging.info(f"An error occurred: {str(e)}")


# if __name__ == "__main__":
#     main()
