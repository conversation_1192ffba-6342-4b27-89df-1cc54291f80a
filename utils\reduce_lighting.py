import cv2
import numpy as np
from pathlib import Path

# To reduce the Lighting and the contrast for an Image Scanned
def reduce_lighting(image_path, output_path):
    # Read the image
    image = cv2.imread(image_path)

    if image is None:
        logging.info("Error: Image not found at the specified path.")
        return

    # Convert the image to the LAB color space
    lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(lab)

    # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization) to the L-channel
    clahe = cv2.createCLAHE(clipLimit=1.0, tileGridSize=(8, 8))
    l_equalized = clahe.apply(l)

    # Merge the CLAHE enhanced L-channel back with A and B channels
    lab_equalized = cv2.merge((l_equalized, a, b))

    # Convert the LAB image back to the BGR color space
    result = cv2.cvtColor(lab_equalized, cv2.COLOR_LAB2BGR)

    # Optionally, apply gamma correction to reduce lighting further
    gamma = 0.8  # Adjust gamma value as needed (<1 for darker, >1 for brighter)
    inv_gamma = 1.0 / gamma
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
    result = cv2.LUT(result, table)

    # Reduce contrast by blending with a neutral gray image
    alpha = 1  # Alpha controls the intensity of contrast reduction (0.0 to 1.0)
    gray = np.full_like(result, 128)  # Create a neutral gray image
    result = cv2.addWeighted(result, alpha, gray, 0.9 - alpha, 0)

    # Save the output image
    cv2.imwrite(output_path, result)
    logging.info(f"Processed image saved at {output_path}")


if __name__ == "__main__":
    image_name = 'reduced_image.jpg'

    output_dir_path = Path("temp_util")
    output_dir_path.mkdir(parents=True, exist_ok=True)  # Ensure directory exists

    output_path = output_dir_path / image_name

    model_image_pairs = [
        # "temp_util/scanner/BL_scanned_1_1.jpg",
        # "temp_util/scanner/COOPER_27122024SCANNEE.jpg",
        # "temp_util/scanner/GPM_27122024SCANNEE.jpg",
        # "temp_util/scanner/SOPHADIM_27122024SCANNEE.jpg",
        "temp_util/scanner/BLS_10_scanned.jpg",
    ]

    image_path = model_image_pairs[0]
    reduce_lighting(image_path, str(output_path))
