import matplotlib
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Optional
import tkinter as tk
from tkinter import ttk, filedialog, scrolledtext, messagebox
from PIL import Image, ImageTk
import os
import cv2
import numpy as np
import torch
from doctr.io import DocumentFile
from doctr.models import ocr_predictor
from doctr.models._utils import estimate_orientation
from typing import List
from dataclasses import dataclass

@dataclass
class OCRResult:
    """Data class to hold OCR results"""
    raw_lines: List[str]
    structured_data: Optional[List[Dict]] = None


class OCRApp:
    def __init__(self, root):
        self.root = root
        self.root.title("OCR Processing Tool")
        self.root.geometry("1200x800")

        # Initialize OCR Service
        self.ocr_service = OCRService()

        # Create main containers
        self.create_gui_elements()

        # Initialize variables
        self.current_image_path = None
        self.current_image = None
        self.processed_image = None

    def create_gui_elements(self):
        # Create main frames
        self.left_frame = ttk.Frame(self.root)
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.right_frame = ttk.Frame(self.root)
        self.right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left frame components (Image display and controls)
        self.create_image_frame()

        # Right frame components (Text output and controls)
        self.create_text_frame()

    def create_image_frame(self):
        # Image display
        self.image_label = ttk.Label(self.left_frame, text="No image selected")
        self.image_label.pack(pady=10)

        # Control buttons
        self.button_frame = ttk.Frame(self.left_frame)
        self.button_frame.pack(pady=5)

        self.select_btn = ttk.Button(
            self.button_frame,
            text="Select Image",
            command=self.select_image
        )
        self.select_btn.pack(side=tk.LEFT, padx=5)

        self.process_btn = ttk.Button(
            self.button_frame,
            text="Process Image",
            command=self.process_image,
            state=tk.DISABLED
        )
        self.process_btn.pack(side=tk.LEFT, padx=5)

        self.save_rotated_btn = ttk.Button(
            self.button_frame,
            text="Save Rotated Image",
            command=self.save_rotated_image,
            state=tk.DISABLED
        )
        self.save_rotated_btn.pack(side=tk.LEFT, padx=5)

        # Angle display
        self.angle_label = ttk.Label(self.left_frame, text="")
        self.angle_label.pack(pady=5)

    def create_text_frame(self):
        # OCR Options
        self.options_frame = ttk.LabelFrame(self.right_frame, text="OCR Options")
        self.options_frame.pack(fill=tk.X, padx=5, pady=5)

        self.use_threshold_var = tk.BooleanVar(value=False)
        self.threshold_check = ttk.Checkbutton(
            self.options_frame,
            text="Use Row Threshold",
            variable=self.use_threshold_var
        )
        self.threshold_check.pack(pady=5)

        # Text output
        self.text_frame = ttk.LabelFrame(self.right_frame, text="OCR Results")
        self.text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.text_output = scrolledtext.ScrolledText(
            self.text_frame,
            wrap=tk.WORD,
            width=50,
            height=30
        )
        self.text_output.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Save button
        self.save_btn = ttk.Button(
            self.right_frame,
            text="Save Results",
            command=self.save_results
        )
        self.save_btn.pack(pady=5)

    def select_image(self):
        file_path = filedialog.askopenfilename(
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.bmp *.tiff *.tif")]
        )

        if file_path:
            self.current_image_path = file_path
            self.load_and_display_image(file_path)
            self.process_btn.config(state=tk.NORMAL)
            self.angle_label.config(text="")
            self.text_output.delete(1.0, tk.END)

    def load_and_display_image(self, image_path):
        # Load image with OpenCV
        self.current_image = cv2.imread(image_path)
        self.display_image(self.current_image)

    def display_image(self, cv2_image):
        # Convert CV2 image to PIL format
        image_rgb = cv2.cvtColor(cv2_image, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(image_rgb)

        # Resize image to fit display area
        display_size = (500, 500)
        pil_image.thumbnail(display_size, Image.Resampling.LANCZOS)

        # Convert to PhotoImage
        photo = ImageTk.PhotoImage(pil_image)

        # Update label
        self.image_label.config(image=photo)
        self.image_label.image = photo

    def process_image(self):
        if not self.current_image_path:
            return

        try:
            # First correct orientation
            orig_img = cv2.imread(self.current_image_path)
            angle = estimate_orientation(orig_img, n_ct=50, ratio_threshold_for_lines=5)
            self.angle_label.config(text=f"Detected angle: {angle:.2f}°")

            # Rotate the image
            rotated_img = self.ocr_service._rotate_image(orig_img, angle)

            # Save the rotated image temporarily
            temp_path = "temp_rotated.jpg"
            cv2.imwrite(temp_path, rotated_img)

            # Display the rotated image
            self.display_image(rotated_img)

            # Perform OCR on the rotated image
            result = self.ocr_service.extract_text(
                temp_path,
                use_row_threshold=self.use_threshold_var.get()
            )

            # Display results
            self.text_output.delete(1.0, tk.END)
            for line in result.raw_lines:
                self.text_output.insert(tk.END, line + '\n')

            # Clean up temporary file
            if os.path.exists(temp_path):
                os.remove(temp_path)

        except Exception as e:
            messagebox.showerror("Error", f"Processing error: {str(e)}")

    def save_results(self):
        if not self.text_output.get(1.0, tk.END).strip():
            messagebox.showwarning("Warning", "No results to save!")
            return

        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )

        if file_path:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(self.text_output.get(1.0, tk.END))
            messagebox.showinfo("Success", "Results saved successfully!")

    def save_rotated_image(self):
        if hasattr(self, 'rotated_image'):
            file_path = filedialog.asksaveasfilename(
                defaultextension=".jpg",
                filetypes=[("JPEG files", "*.jpg"), ("All files", "*.*")]
            )
            if file_path:
                cv2.imwrite(file_path, self.rotated_image)
                messagebox.showinfo("Success", "Rotated image saved successfully!")

    def update_buttons(self):
        if hasattr(self, 'rotated_image'):
            self.save_rotated_btn.config(state=tk.NORMAL)
        else:
            self.save_rotated_btn.config(state=tk.DISABLED)

class OCRService:
    """Global OCR service for text extraction from images"""

    def __init__(self):
        # Even with detect_orientation=True, older versions of docTR might not store page.angle
        self.model = ocr_predictor(
            det_arch='db_resnet50',
            reco_arch='crnn_vgg16_bn',
            detect_orientation=True,
            pretrained=True
        )

    def calculate_dynamic_threshold(self, line_heights: List[float]) -> float:
        """
        Calculate dynamic row threshold with adjusted parameters for better line merging
        """
        if not line_heights:
            return 0.015  # Default fallback

        sorted_heights = sorted(line_heights)
        q1 = sorted_heights[len(sorted_heights) // 4]
        q3 = sorted_heights[3 * len(sorted_heights) // 4]
        iqr = q3 - q1
        filtered_heights = [h for h in line_heights if q1 - 1.5 * iqr <= h <= q3 + 1.5 * iqr]

        avg_height = sum(filtered_heights) / len(filtered_heights)
        std_dev = (sum((x - avg_height) ** 2 for x in filtered_heights) / len(filtered_heights)) ** 0.5

        return avg_height * 0.75 + std_dev * 0.5

    def extract_text(self, image_path: str, use_row_threshold: bool = False) -> OCRResult:
        """
        Extract text from image with optional row threshold processing
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image not found: {image_path}")

        # Load and process image with docTR
        doc = DocumentFile.from_images(image_path)
        result = self.model(doc)

        # Initialize containers
        raw_lines = []
        current_line_entries = []  # Stores tuples of (x_start, text)
        current_line_y = None
        row_threshold = None

        # Calculate row threshold if needed
        if use_row_threshold:
            line_heights = []
            for page in result.pages:
                for block in page.blocks:
                    for line in block.lines:
                        height = line.geometry[1][1] - line.geometry[0][1]
                        if height > 0:
                            line_heights.append(height)
            row_threshold = self.calculate_dynamic_threshold(line_heights)

        # Process OCR results
        for page in result.pages:
            for block in page.blocks:
                for line in block.lines:
                    # Calculate geometry properties
                    y_center = (line.geometry[0][1] + line.geometry[1][1]) / 2
                    x_start = line.geometry[0][0]
                    text = ' '.join(word.value for word in line.words).strip()

                    if use_row_threshold:
                        # If it's the first line, just store it
                        if current_line_y is None:
                            current_line_y = y_center
                            current_line_entries.append((x_start, text))
                        # If the line is close to the current line's y, merge them
                        elif abs(y_center - current_line_y) <= row_threshold:
                            current_line_entries.append((x_start, text))
                        else:
                            # Sort the collected entries by x_start and append them
                            sorted_entries = sorted(current_line_entries, key=lambda x: x[0])
                            raw_lines.append(' '.join([entry[1] for entry in sorted_entries]))

                            # Reset for the new line
                            current_line_entries = [(x_start, text)]
                            current_line_y = y_center
                    else:
                        # Without threshold, each line is processed individually
                        raw_lines.append(text)

        # Add remaining entries if any
        if use_row_threshold and current_line_entries:
            sorted_entries = sorted(current_line_entries, key=lambda x: x[0])
            raw_lines.append(' '.join([entry[1] for entry in sorted_entries]))

        return OCRResult(raw_lines=raw_lines)

    def correct_image_orientation(self, image_path: str, show_result: bool = True) -> float:
        """
        Detect orientation of the image by analyzing contours (via estimate_orientation),
        correct it, and optionally display the result.
        Returns the detected angle in degrees (negative => rotate by +angle to correct).
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image not found: {image_path}")

        # Load the original image with OpenCV for orientation detection
        orig_img = cv2.imread(image_path, cv2.IMREAD_COLOR)
        if orig_img is None:
            raise ValueError(f"Could not load image with OpenCV: {image_path}")

        # Use docTR's estimate_orientation to get the dominant page angle
        # +angle => page is rotated CCW by `angle` degrees
        # -angle => page is rotated CW by `abs(angle)` degrees
        angle = estimate_orientation(orig_img, n_ct=50, ratio_threshold_for_lines=5)
        print(f"[INFO] Detected angle (counter-clockwise): {angle:.2f} degrees")

        # Rotate the image in the opposite direction to correct it
        rotated_img = self._rotate_image(orig_img, angle)

        # -- Display result if requested
        if show_result:
            self._show_image("Corrected Image", rotated_img)

        return angle

    def _rotate_image(self, image: np.ndarray, angle: float) -> np.ndarray:
        """
        Rotate an image around its center by the specified angle.
        Here, angle>0 means the image is CCW by that angle.
        To correct it, we rotate by -angle.
        """
        if abs(angle) < 0.5:
            # If angle is negligible, skip to save computation
            return image

        (h, w) = image.shape[:2]
        center = (w // 2, h // 2)

        # We rotate by -angle to "undo" the detected CCW angle
        M = cv2.getRotationMatrix2D(center, -angle, 1.0)
        rotated = cv2.warpAffine(
            image,
            M,
            (w, h),
            flags=cv2.INTER_CUBIC,
            borderMode=cv2.BORDER_REPLICATE
        )
        return rotated

    def _show_image(self, title: str, image: np.ndarray):
        """
        Show an image using matplotlib (converted from BGR -> RGB).
        """
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        plt.figure(figsize=(8, 6))
        plt.title(title)
        plt.imshow(image_rgb)
        plt.axis('off')
        plt.show()


# Usage example:
# def main():
#     # Set the backend to TkAgg (or another if you prefer)
#     matplotlib.use('TkAgg')
#
#     # Print PyTorch version and CUDA info first
#     print(f"Using PyTorch version: {torch.__version__}")
#     print(f"CUDA available: {torch.cuda.is_available()}")
#     if torch.cuda.is_available():
#         print(f"CUDA device: {torch.cuda.get_device_name(0)}")
#
#     # Instantiate the OCR service
#     ocr_service = OCRService()
#
#     # Replace with your test image path
#     image_path = r'C:\path\to\your\image.jpg'
#
#     # 1) Correct the image orientation and display it
#     try:
#         angle_detected = ocr_service.correct_image_orientation(image_path, show_result=True)
#         print(f"Orientation angle detected (CCW): {angle_detected:.2f} degrees")
#     except Exception as e:
#         print(f"Orientation correction error: {e}")
#
#     print("\n" + "=" * 50 + "\n")
#
#     # 2) OCR example without row threshold
#     try:
#         result_no_threshold = ocr_service.extract_text(image_path, use_row_threshold=False)
#         print("Without row threshold:")
#         for line in result_no_threshold.raw_lines:
#             print(line)
#     except Exception as e:
#         print(f"OCR without threshold error: {e}")
#
#     print("\n" + "=" * 50 + "\n")
#
#     # 3) OCR example with row threshold
#     try:
#         result_with_threshold = ocr_service.extract_text(image_path, use_row_threshold=True)
#         print("With row threshold:")
#         for line in result_with_threshold.raw_lines:
#             print(line)
#     except Exception as e:
#         print(f"OCR with threshold error: {e}")

def main():
    root = tk.Tk()
    app = OCRApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
