import os

import cv2
import pytesseract
from PIL import Image
import re
from pathlib import Path
from src.app.utils.document_model import DocumentModel
from src.app.services.ocr_service import OCRService
from src.app.utils import constants
from src.app.config import SYS_ARGV
from src.app.utils.helpers import *
import traceback
import logging

# Initialize OCR service at the top of the file after imports
ocr_service = OCRService()

# Assuming main.py is in the root of your project directory
# Assuming main.py is in the root of your project directory
if SYS_ARGV == 'api':
    base_path = Path(__file__).resolve().parent.parent.parent.parent  # Adjust based on your file structure
else:
    base_path = Path(__file__).resolve().parent.parent.parent.parent

# logging.info("base_path header: ", base_path)

# Example to access a temp directory at the root of the project
temp_path = base_path / 'temp'

# # Ensure the directory exists
# temp_path.mkdir(exist_ok=True)

""" -- ******* ------------------------- O<PERSON> ( Part Client of Header Image )  ------------------------- ******* --"""


# Revised clean_ocr_results function
def clean_ocr_results(ocr_results, model, document_model_header):
    # Dictionary of configurations for different models, mapping keywords to actions and processing options
    model_config = constants.model_config_header

    # Initialize a new document model
    # document_model_header = DocumentModel()
    document_model_header.header.info_client = ""

    # Precompiled patterns for numbers and dates
    ice_pattern = re.compile(r'\b\d{10,}\b')
    number_pattern = re.compile(r'\b\d{4,}\b')
    num_client_pattern = re.compile(r'\b\d{4,6}\b')
    num_bl_pattern = re.compile(r'\b\d{5,7}\b')
    patente_client_pattern = re.compile(r'\b\d{5,}\b')
    date_pattern = re.compile(r'\b\d{2}[\./-]\d{2}[\./-]\d{2,4}\b')

    # Enhanced the code logic and efficiency
    for values_text in ocr_results.values():
        # logging.info(values_text)
        # logging.info("\n\n\n")
        values = values_text.split('\n')
        for value in values:
            line = value.upper()
            # logging.info("--line ___ : ", line)

            if model in constants.list_models:
                # Common handling for client information
                if keyword_in_text(model_config['CLIENT_BOX'], values_text) or keyword_in_text(model_config['ICE'], values_text):

                    # Special condition for COOPER_PHARMA_CASA model
                    if model == constants.COOPER_PHARMA_CASA and not (
                            keyword_in_text(model_config['CLIENT_BOX_NOT_IN_COOPER'], values_text) or
                            re.findall(date_pattern, values_text) or keyword_in_text(model_config['ICE'], line)):
                        document_model_header.header.info_client += " " + line
                    else:
                        document_model_header.header.info_client += " " + line if not keyword_in_text(model_config['ICE'], line) and not keyword_in_text(model_config['CLIENT_BOX'], line) else ""

                    if keyword_in_text(model_config['ICE'], line):
                        ice_numbers = re.findall(ice_pattern, values_text)
                        if ice_numbers:
                            document_model_header.header.ice_client = ice_numbers[0]
                            # break

                    if keyword_in_text(model_config['PATENTE'], line):
                        patente_numbers = re.findall(patente_client_pattern, values_text)
                        if patente_numbers:
                            document_model_header.header.patente_client = patente_numbers[0]
                            # break

                # Handling for document ID
                if keyword_in_text(model_config['DOCUMENT_ID'], values_text) and not keyword_in_text(model_config['PATENTE'], values_text):
                    if not document_model_header.header.num_bl:
                        num_bl_list = re.findall(num_bl_pattern, values_text)
                        document_model_header.header.num_bl = num_bl_list[0] if num_bl_list else None
                        # break

                # Handling for date
                if keyword_in_text(model_config['DATE_BL'], values_text):
                    if not document_model_header.header.date_bl:
                        date_bl_list = re.findall(date_pattern, values_text)
                        document_model_header.header.date_bl = date_bl_list[0] if date_bl_list else None
                        # break

                # Handling for client number
                if keyword_in_text(model_config['NUM_CLIENT'], values_text):
                    if not document_model_header.header.num_client:
                        num_client_list = re.findall(num_client_pattern, values_text)
                        document_model_header.header.num_client = num_client_list[0] if num_client_list else None
                        # break

                # Additional processing for pages, zones, sectors, accounts can be added here as needed

    return document_model_header


# Annotation Image - export all boxes that contain text
def processing_countours_draw(image, random_id, model):
    """Annotate image by drawing contours around detected single-cell tables."""
    try:
        original_image = image.copy()

        # Convert the image to gray scale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Use Canny edge detection to find edges in the image
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)

        # Find contours in the edges image
        contours, _ = cv2.findContours(edges, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        # Filter out the contours to keep only those corresponding to single-cell tables
        single_cell_tables = []
        for cnt in contours:
            x, y, w, h = cv2.boundingRect(cnt)  # Get the bounding box dimensions

            # Define your criteria for a single-cell table. For example:
            # - The width should be significantly greater than the height (or vice versa).
            # - The area should be above a certain threshold.
            # - The aspect ratio should fall within a specific range.
            # You can adjust these thresholds according to your specific needs.
            aspect_ratio = w / float(h)
            min_width = 150
            min_height = 50
            aspect_ratio_threshold = 1  # Example threshold for aspect ratio

            if w > min_width and h > min_height and aspect_ratio > aspect_ratio_threshold:
                single_cell_tables.append(cnt)

        # Draw the rectangles for single-cell tables
        for rect in single_cell_tables:
            x, y, w, h = cv2.boundingRect(rect)
            cv2.rectangle(original_image, (x, y), (x + w, y + h), (0, 255, 0), 2)

        # # Save and show the annotated image
        # annotated_image_image = 'annotated_BLS_header_' + random_id + '_' + model + '.jpg'
        # annotated_image_path = temp_path / f"{annotated_image_image}"
        # cv2.imwrite(str(annotated_image_path), original_image)

        return single_cell_tables, original_image

    except Exception as e:
        logging.error(f"Error during contour processing: {e}")
        return [], image


# Final Extract Data of Client
def OCR_Client_Info(image_path, model, document_model_header, module, random_id):
    """Extract client information from the header part of the image."""
    try:
        image = cv2.imread(image_path)

        # Export all boxes and the original image after do tha Annotation for the image
        single_cell_tables, original_image = processing_countours_draw(image, random_id, model)

        # # Now we will perform OCR on each detected rectangle (box)
        ocr_results = {}
        for idx, rect in enumerate(single_cell_tables):
            # Get the bounding box for the rectangle
            x, y, w, h = cv2.boundingRect(rect)
            # Crop the region of interest based on the bounding box
            roi = original_image[y:y + h, x:x + w]
            # Convert to PIL Image format for pytesseract
            roi_pil = Image.fromarray(cv2.cvtColor(roi, cv2.COLOR_BGR2RGB))
            # Perform OCR on the cropped image
            custom_config = r'--oem 3 --psm 6'

            # if module != 'default':
            #     text = pytesseract.image_to_string(roi_pil, config=custom_config, lang=module)
            # else:
            #     text = pytesseract.image_to_string(roi_pil, config=custom_config)


            ## docTR ocr
            # Save the ROI as temporary image
            temp_roi_path = str(temp_path / f"temp_roi_{random_id}_{idx}.jpg")
            roi_pil.save(temp_roi_path)
            # Extract text using OCR service
            result = ocr_service.extract_text(temp_roi_path, use_row_threshold=False)
            text = ' '.join(result.raw_lines)
            # Clean up temporary file
            os.remove(temp_roi_path)


            # Store the OCR results
            ocr_results[f'box_{idx + 1}'] = text.strip()

        # Clean the OCR results
        document_header = clean_ocr_results(ocr_results, model, document_model_header)

        return document_header

    except FileNotFoundError as fnf_error:
        logging.error(f"Erreur de fichier: {fnf_error}")
        raise fnf_error
    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode OCR_Client_Info: {traceback.format_exc()}")
        raise e


""" -- ******* ------------------------- OCR ( Part Supplier of Header Image )  ------------------------- ******* --"""


# Remove the part Empty white in top of image
def trim_whitespace(image_path, random_id, model):
    """
    Trims the whitespace from the top of an image.

    :param model:
    :param random_id:
    :param image_path: Path to the image to be trimmed.
    :return: A trimmed PIL image object.
    """

    """Trim whitespace from the top of an image."""
    try:
        with Image.open(image_path) as img:
            # Convert to grayscale
            grayscale = img.convert("L")
            # Use point function to find where the image is not white
            non_white_areas = grayscale.point(lambda x: 0 if x > 200 else 255)
            # Find bounding box, which is the left, upper, right, lower pixel coordinates
            bbox = non_white_areas.getbbox()

            # If no content is found in the image, return the original image
            if not bbox:
                return img

            # Determine where to crop based on the bounding box, we only want to trim the top
            left, upper, right, lower = bbox
            new_upper = upper if upper > 0 else 0
            new_lower = img.height

            # Crop the image accordingly
            trimmed_img = img.crop((left, new_upper, right, new_lower))

            # Save the trimmed image
            trimmed_image_image = 'trimmed_BLS_header_' + random_id + '_' + model + '.jpg'
            trimmed_image_path = temp_path / f"{trimmed_image_image}"
            trimmed_img.save(trimmed_image_path)

            return trimmed_image_path

    except Exception as e:
        logging.error(f"Error during whitespace trimming: {e}")
        raise e


# Crop the Content of supplier in the header Image
def crop_image_part_supplier(random_id, model, image_path, crop_left=0.6, crop_top=0.6):
    """
    Crop the image to the specified proportions.

    :param model:
    :param random_id:
    :param image_path: Path to the image to be cropped.
    :param crop_left: Proportion of the left part of the image to retain.
    :param crop_top: Proportion of the top part of the image to retain.
    :return: Cropped image object.
    """

    """Crop the image to specified proportions for the supplier part."""
    try:
        # Ensure temp directory exists
        temp_path.mkdir(parents=True, exist_ok=True)

        # Validate input path
        image_path = Path(image_path)
        if not image_path.exists():
            raise FileNotFoundError(f"Image file not found: {image_path}")

        # Open and process image
        with Image.open(image_path) as img:
            # Convert to grayscale
            grayscale = img.convert("L")
            non_white_areas = grayscale.point(lambda x: 0 if x > 200 else 255)
            bbox = non_white_areas.getbbox()

            if not bbox:
                trimmed_img = img.copy()
            else:
                left, upper, right, lower = bbox
                new_upper = upper if upper > 0 else 0
                new_lower = img.height
                trimmed_img = img.crop((left, new_upper, right, new_lower))

            # # Save trimmed image
            # trimmed_image_name = f'trimmed_BLS_header_{random_id}_{model}.jpg'
            # trimmed_image_path = temp_path / trimmed_image_name
            # trimmed_img.save(str(trimmed_image_path))

            # Crop the trimmed image
            width, height = trimmed_img.size
            right = int(crop_left * width)
            bottom = int(crop_top * height)
            cropped_img = trimmed_img.crop((0, 0, right, bottom))

            # # Save cropped image
            # cropped_image_name = f'cropper_BLS_header_{random_id}_{model}.jpg'
            # cropped_image_path = temp_path / cropped_image_name
            # cropped_img.save(str(cropped_image_path))

            return cropped_img

    except Exception as e:
        logging.error(f"Error during image cropping: {traceback.format_exc()}")
        # If there's an error, try to return the original image
        try:
            return Image.open(image_path)
        except:
            # If we can't open the original image either, raise the original error
            raise e


# Identify the data of Supplier of the Output OCR
def identify_supplier_info(ocr_output, model):
    """Identify supplier information from OCR output."""
    try:
        # Initialize a new document model
        document_model_header = DocumentModel()

        # Dictionary of configurations for different models, mapping keywords to actions and processing options
        model_config = constants.model_config_header

        # pattern ICE Supplier
        gloabl_ocr_lines_supp = ocr_output.split('\n')

        # for line in gloabl_ocr_lines_supp:
        #     logging.info('___ line : ___ ',line)

        # ice_number_pattern = re.compile(r'\b\d{10,}\b')
        # patente_number_pattern = re.compile(r'\b\d{8,}\b')
        # cnss_number_pattern = re.compile(r'\b\d{7,}\b')

        number_pattern = re.compile(r'\b\d{4,}\b')

        # Normalize to lowercase for case-insensitive matching
        ocr_output = ocr_output.lower()

        # Supplier patterns and their respective names
        supplier_patterns = constants.supplier_patterns

        address_pattern = model_config['ADDRESS_SUPPLIER']

        # Initialize the supplier name as unknown by default
        supplier_name = 'Unknown'

        # Check each supplier's patterns to see if they are in the OCR output
        for supplier, patterns in supplier_patterns.items():
            for pattern in patterns:
                if re.search(pattern, ocr_output):
                    supplier_name = supplier.capitalize()
                    break  # Stop searching if a pattern is found
            if supplier_name != 'Unknown':  # If found, no need to check other suppliers
                break

        document_model_header.header.name_fournisseur = supplier_name

        for line in gloabl_ocr_lines_supp:
            line = line.upper()

            # Extract the ICE Number of Supplier from the OCR
            if keyword_in_text(model_config['ICE'], line):
                ice_matches = re.findall(number_pattern, line)
                if ice_matches:
                    ice_number_exported = ice_matches[0]
                    document_model_header.header.ice_fournisseur = ice_number_exported

            # Extract the Patente Number of Supplier from the OCR
            if keyword_in_text(model_config['PATENTE'], line):
                patente_matches = re.findall(number_pattern, line)
                if patente_matches:
                    patente_number_exported = patente_matches[0]
                    document_model_header.header.patente_fournisseur = patente_number_exported

            # Extract the CNSS Number of Supplier from the OCR
            if keyword_in_text(model_config['CNSS'], line):
                cnss_matches = re.findall(number_pattern, line)
                if cnss_matches:
                    cnss_number_exported = cnss_matches[0]
                    document_model_header.header.cnss_fournisseur = cnss_number_exported

            # Extract the phones Number of Supplier from the OCR
            if keyword_in_text(model_config['CONTACT_INFO'], line):
                # Pattern to match phone numbers, possibly prefixed by 'Fax'
                phone_number_pattern = re.compile(
                    r'\b(?:Fax\s*)?(\d{2,4}[\s\-\.]{0,1}\d{2,4}[\s\-\.]{0,1}\d{2,4}[\s\-\.]{0,1}\d{0,4})\b')

                # Find all matches in the text
                matches = re.findall(phone_number_pattern, line)

                # Post-process matches to clean and standardize them
                phone_numbers = [''.join(re.findall(r'\d+', match)) for match in matches]
                document_model_header.header.contact_info_fournisseur = phone_numbers

        # Check address supplier's
        document_model_header.header.adresse_fournisseur = ""
        for line in gloabl_ocr_lines_supp:
            line = line.upper()
            for pattern in address_pattern:
                if re.search(pattern, line):
                    document_model_header.header.adresse_fournisseur += " ," + line
                    break

        return document_model_header

    except Exception as e:
        logging.error(f"Error in identify_supplier_info: {e}")
        raise e

def ensure_temp_directory():
    """Ensure temporary directory exists"""
    temp_path.mkdir(parents=True, exist_ok=True)

def validate_image_path(image_path):
    """Validate that image path exists and is readable"""
    path = Path(image_path)
    if not path.exists():
        raise FileNotFoundError(f"Image file not found: {image_path}")
    if not path.is_file():
        raise ValueError(f"Path is not a file: {image_path}")
    return str(path)


# Final Extract Data of Supplier
def OCR_Supplier_Info(image_path, model, module, random_id):
    """Extract supplier information from the header part of the image."""
    try:
        # Ensure temp directory exists
        ensure_temp_directory()

        # Validate image path
        image_path = validate_image_path(image_path)

        # # Trim and Crop the header of image (part supplier)
        # cropped_image = crop_image_part_supplier(random_id, model, image_path)

        # Image without crop
        cropped_image = Image.open(image_path)

        # # Perform OCR on the cropped image
        # custom_config = r'--oem 3 --psm 6'
        # if module != 'default':
        #     ocr_result_supplier = pytesseract.image_to_string(cropped_image, config=custom_config, lang=module)
        # else:
        #     ocr_result_supplier = pytesseract.image_to_string(cropped_image, config=custom_config)

        ## docTR ocr
        # Save cropped image to temporary file
        temp_image_path = str(temp_path / f"temp_supplier_{random_id}.jpg")
        cropped_image.save(temp_image_path)

        # Extract text using OCR service
        result = ocr_service.extract_text(temp_image_path, use_row_threshold=False)
        ocr_result_supplier = ' '.join(result.raw_lines)

        # Clean up temporary file
        os.remove(temp_image_path)



        # # Output the beginning of the OCR result for verification (full result may be lengthy)
        # beginning_of_result = ocr_result_supplier[:100]

        # get The Supplier name
        document_model_header = identify_supplier_info(ocr_result_supplier, model)

        return document_model_header

    except FileNotFoundError as fnf_error:
        logging.error(f"Erreur de fichier: {fnf_error}")
        raise fnf_error
    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode OCR_Supplier_Info: {traceback.format_exc()}")
        raise e



""" -- ******* ------------------ Merge Output OCR ( Part Supplier of Header Image )  ------------------ ******* --"""


# Final Extract Data of Header part
def OCR_All_HEADER(image_path, model, random_id, module='default'):
    """
    Process header information from an image.

    Args:
        image_path: Path to the image file
        model: Model identifier
        random_id: Random identifier for the image
        module: OCR module to use (default='default')

    Returns:
        DocumentModel object containing header information
    """
    try:
        # Validate input path exists
        image_path = Path(image_path)
        if not image_path.exists():
            logging.error(f"Image file not found: {image_path}")
            raise FileNotFoundError(f"Image file not found: {image_path}")

        # Ensure temp directory exists
        temp_path.mkdir(parents=True, exist_ok=True)

        # Initialize document model
        document_model_header = DocumentModel()

        try:
            # Export Data of Supplier of the Header image
            document_model_header = OCR_Supplier_Info(str(image_path), model, module, random_id)
        except Exception as e:
            logging.warning(f"Error processing supplier info: {e}")
            # Continue processing even if supplier info fails

        try:
            # Export Data of Client of the Header image
            document_model_header = OCR_Client_Info(
                str(image_path),
                model,
                document_model_header,
                module,
                random_id
            )
        except Exception as e:
            logging.warning(f"Error processing client info: {e}")
            # Continue processing even if client info fails

        return document_model_header

    except Exception as e:
        logging.error(f"Error in OCR_All_HEADER: {traceback.format_exc()}")
        # Return empty document model instead of raising exception
        return DocumentModel()


""" -- ******* ---------------------------------------- Testing  --------------------------------------- ******* --"""

# image_path = r'C:\Users\<USER>\Downloads\Work __Abderrahmane_ouhna\OCR_DOCUMENT_GROSSISTE\ocr_document_grossiste\temp\identify_suppliers\bl_identify_supplier_d1ed6e9d__processed.jpg'
# result = OCR_All_HEADER(image_path, 'GLOBAL', '000000')
# print (result)
