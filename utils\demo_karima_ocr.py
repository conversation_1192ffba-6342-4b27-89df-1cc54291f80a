import cv2
import pytesseract
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
from transformers import pipeline


def preprocess_image(image_path):
    # Ouvrir l'image avec PIL
    image = Image.open(image_path)
    # Convertir l'image en niveaux de gris
    image = image.convert('L')
    # Appliquer un filtre pour améliorer la netteté
    image = image.filter(ImageFilter.SHARPEN)
    # Améliorer le contraste
    enhancer = ImageEnhance.Contrast(image)
    image = enhancer.enhance(2)
    # Sauvegarder l'image prétraitée
    preprocessed_image_path = "preprocessed_image.png"
    image.save(preprocessed_image_path)
    return preprocessed_image_path


def extract_text(image_path):
    custom_config = r'--oem 3 --psm 6'
    # Prétraiter l'image
    preprocessed_image_path = preprocess_image(image_path)
    # Utiliser pytesseract pour extraire le texte de l'image
    text = pytesseract.image_to_string(Image.open(preprocessed_image_path), config=custom_config, lang='eng')
    return text


def save_text_to_file(text, file_path):
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(text)


def process_with_transformers(text):
    # Utiliser un pipeline de transformers pour traiter le texte
    summarizer = pipeline("summarization")
    summary = summarizer(text, max_length=512, min_length=30, do_sample=False)
    return summary[0]['summary_text']


if __name__ == "__main__":
    # Chemin vers l'image et les fichiers de sortie
    image_path = 'temp_util/origin_BL/BLS_1.jpg'
    text_file_path = 'extracted_text.txt'
    # Extraire le texte de l'image
    extracted_text = extract_text(image_path)
    logging.info("Texte extrait :")
    logging.info(extracted_text)
    # Sauvegarder le texte extrait dans un fichier texte
    save_text_to_file(extracted_text, text_file_path)
    logging.info(f"Texte extrait sauvegardé dans {text_file_path}")
    # Traiter le texte avec Transformers
    processed_text_transformers = process_with_transformers(extracted_text)
    logging.info("Texte après traitement avec Transformers :")
    logging.info(processed_text_transformers)
