import cv2
import pytesseract
from PIL import Image
from pathlib import Path
from src.app.utils.document_model import DocumentModel
from src.app.utils import constants, models_utils
from src.app.utils.helpers import *
from src.app.config import SYS_ARGV
import logging
from src.app.services.ocr_service import OCRService

# Initialize OCR service at the top of the file after imports
ocr_service = OCRService()

# Assuming main.py is in the root of your project directory
if SYS_ARGV == 'api':
    base_path = Path(__file__).resolve().parent.parent.parent.parent  # Adjust based on your file structure
else:
    base_path = Path(__file__).resolve().parent.parent.parent.parent

# logging.info("base_path footer: ", base_path)


# Example to access a temp directory at the root of the project
temp_path = base_path / 'temp'


# # Ensure the directory exists
# temp_path.mkdir(exist_ok=True)

# Define a function to process the next value in the list based on specified conditions
def process_next_value(attribute, model, values, idx, correction_dict, split_condition=3, replace_chars=None, divider=1,
                       multi_value=False,  is_number=True):
    """Process the next value in the list, with optional character replacements and division."""
    # Get the next value if it exists and meets the condition of having fewer splits than split_condition
    next_value = values[idx + 1] if (idx + 1) < len(values) and len(
        values[idx + 1].split()) < split_condition else 0

    # Check if the line is not a number (ex: Date Debut, Date Fin, ...)
    if not is_number:
        return [{attribute: next_value}]

    # Check if the line has Multi-values of attributes
    if multi_value:
        next_value = values[idx + 1]
        if replace_chars:
            for old, new in replace_chars:
                next_value = str(next_value).replace(old, new)

        data_multi_key_values = []
        if model == constants.SOPHADIMS:
            next_value = remove_pipe_if_not_merge_with_number(
                next_value)  # replace the '|' character if not merged by another word

            next_value_list = next_value.split()
            data_multi_key_values.append(
                {'total_ppv': models_utils.correct_number_except_coma(next_value_list[-1])})
            data_multi_key_values.append(
                {'total_produit_net_ttc': models_utils.correct_number_except_coma(next_value_list[0])})

            return data_multi_key_values

    # logging.info("next_value ", values[idx + 1])
    # If replacement characters are specified, replace them in the next value
    if next_value != 0:
        next_value = remove_pipe_if_not_merge_with_number(
            next_value)  # replace the '|' character if not merged by another word
        if replace_chars:
            for old, new in replace_chars:
                next_value = str(next_value).replace(old, new)

        next_value = remove_pipe_if_not_merge_with_number(
            next_value)  # replace the '|' character if not merged by another word

        try:
            # Correct the number format of the next value using the specified correction dictionary
            corrected_value = models_utils.correct_number_except_coma(next_value)
            if divider != 1:
                corrected_value = float(corrected_value) / divider
            return [{attribute: corrected_value}]
        except ValueError:
            # If there's a ValueError, return the value as is
            return [{attribute: next_value}]

    return [{attribute: next_value}]


""" -- ******* ------------------------- OCR ( Part Footer of Header Image )  ------------------------- ******* --"""


# Define the main function to clean OCR results based on the given model
def clean_ocr_results(ocr_results, model):
    # Dictionary of configurations for different models, mapping keywords to actions and processing options
    model_config = constants.model_config_footer

    # Initialize a new document model
    document_model_footer = DocumentModel()

    # Iterate through the OCR results
    for index, values_text in enumerate(ocr_results.values()):
        values = values_text.split('\n')
        for idx, value in enumerate(values):
            # Retrieve the configuration for the current model
            config = model_config.get(model, [])
            for keywords, attribute, options in config:
                # Check if the current value contains any of the specified keywords and if the corresponding attribute is not yet set
                if keyword_in_text(keywords, value) and not getattr(document_model_footer.footer, attribute):

                    multi_val_in_line = options.get('multi_value', False)
                    # check the next line after find the pattern is contained a number or not

                    # if idx + 1 >= len(values) - 1:
                    if idx + 1 < len(values):
                        if contains_number(values[idx + 1]):
                            processed_values = process_next_value(attribute, model, values, idx,
                                                                  constants.quantity_correction_dict,
                                                                  **options)
                            for data in processed_values:
                                for key, value in data.items():
                                    if not getattr(document_model_footer.footer, key):
                                        setattr(document_model_footer.footer, key, value)

    return document_model_footer


# Annotation Image - export all boxes that contain text
def processing_countours_draw(image, model, random_id):
    """Export all boxes that contain text and annotate the image."""
    try:
        original_image = image.copy()

        setting_min_surface = constants.width_height_boxes_perc_footer.get(model)

        # Convert the image to gray scale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Use Canny edge detection to find edges in the image
        edges = cv2.Canny(gray, 30, 200, apertureSize=3)  # Lower the threshold to detect more edges

        # Find contours in the edges image
        contours, _ = cv2.findContours(edges, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        # Optionally remove the contours by filling them in
        cv2.drawContours(original_image, contours, -1, (255, 255, 255), thickness=cv2.FILLED)

        # Filter out the contours to keep only those corresponding to single-cell tables
        single_cell_tables = []
        for cnt in contours:
            x, y, w, h = cv2.boundingRect(cnt)  # Get the bounding box dimensions

            # Define your criteria for a single-cell table. For example:
            # - The width should be significantly greater than the height (or vice versa).
            # - The area should be above a certain threshold.
            # - The aspect ratio should fall within a specific range.
            # You can adjust these thresholds according to your specific needs.
            aspect_ratio = w / float(h)

            min_width = original_image.shape[1] * setting_min_surface.get('width')  # 5% of image width
            min_height = original_image.shape[0] * setting_min_surface.get('height')  # 5% of image height
            aspect_ratio_threshold = 0.5  # Less restrictive

            if w > min_width and h > min_height and aspect_ratio > aspect_ratio_threshold:
                single_cell_tables.append(cnt)

        # Draw the rectangles for single-cell tables
        for rect in single_cell_tables:
            x, y, w, h = cv2.boundingRect(rect)
            # cv2.drawContours(original_image, contours, -1, (255, 255, 255), thickness=cv2.FILLED)
            cv2.rectangle(original_image, (x, y), (x + w, y + h), (0, 255, 0), 2)

        # Save and show the annotated image
        annotated_image_image = 'annotated_BLS_footer_' + random_id + '_' + model + '.jpg'
        annotated_image_path = temp_path / f"{annotated_image_image}"
        cv2.imwrite(str(annotated_image_path), original_image)

        return single_cell_tables, original_image

    except Exception as e:
        logging.error(f"Error during contour processing: {e}")
        return [], image


# Final Extract Data of Footer
def OCR_Footer_Info(image_path, model, module, random_id):
    """Extract data from the footer of the image."""
    try:
        image = cv2.imread(image_path)

        # Export all boxes and the original image after do tha Annotation for the image
        single_cell_tables, original_image = processing_countours_draw(image, model, random_id)

        # # Now we will perform OCR on each detected rectangle (box)
        ocr_results = {}
        for idx, rect in enumerate(single_cell_tables):
            # Get the bounding box for the rectangle
            x, y, w, h = cv2.boundingRect(rect)
            # Crop the region of interest based on the bounding box
            roi = original_image[y:y + h, x:x + w]
            # Convert to PIL Image format for pytesseract
            roi_pil = Image.fromarray(cv2.cvtColor(roi, cv2.COLOR_BGR2RGB))
            # Perform OCR on the cropped image
            custom_config = r'--oem 3 --psm 6'

            # if module != 'default':
            #     text = pytesseract.image_to_string(roi_pil, config=custom_config, lang=module)
            # else:
            #     text = pytesseract.image_to_string(roi_pil, config=custom_config)


            ## DocTR - OCR
            # Save the ROI as temporary image
            temp_roi_path = str(temp_path / f"temp_roi_{random_id}_{idx}.jpg")
            roi_pil.save(temp_roi_path)
            # Extract text using OCR service
            result = ocr_service.extract_text(temp_roi_path, use_row_threshold=False)
            text = ' '.join(result.raw_lines)
            # # Clean up temporary file
            # os.remove(temp_roi_path)


            # Store the OCR results
            ocr_results[f'box_{idx + 1}'] = text.strip()

        # Clean the OCR results
        document_model_footer = clean_ocr_results(ocr_results, model)

        return document_model_footer

    except Exception as e:
        logging.error(f"Error during OCR processing: {e}")
        raise e


""" -- ******* ------------------ Merge Output OCR ( Part Supplier of Header Image )  ------------------ ******* --"""


# Final Extract Data of Header part
def OCR_All_FOOTER(image_path, model, random_id, module='default'):
    """Extract all footer data from the image."""
    try:
        document_model_footer = OCR_Footer_Info(image_path, model, module, random_id)
        return document_model_footer
    except Exception as e:
        logging.error(f"Error in OCR_All_FOOTER: {e}")
        return None


""" -- ******* ---------------------------------------- Testing  --------------------------------------- ******* -- """

# image_path = '../data/output_preprocessing/BL_footer_cropped_processed.jpg'
# result = OCR_All_FOOTER(image_path)
# print (result)
