import cv2
import numpy as np
from matplotlib import pyplot as plt

def load_image(path):
    image = cv2.imread(path)
    return image

def preprocess_image(image):
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    # Apply Gaussian blur
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    # Edge detection
    edged = cv2.Canny(blurred, 50, 150)
    return edged

def find_document_contours(edged):
    # Find contours in the edged image
    contours, _ = cv2.findContours(edged, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    # Sort the contours by area, descending order
    contours = sorted(contours, key=cv2.contourArea, reverse=True)
    # Loop over the contours to find the document
    for contour in contours:
        # Approximate the contour
        peri = cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, 0.02 * peri, True)
        # If the contour has four points, assume it is the document
        if len(approx) == 4:
            return approx
    return None

def order_points(pts):
    # Initialize a list of coordinates that will be ordered
    # as top-left, top-right, bottom-right, and bottom-left
    rect = np.zeros((4, 2), dtype="float32")
    # The top-left point will have the smallest sum, whereas
    # the bottom-right point will have the largest sum
    s = pts.sum(axis=1)
    rect[0] = pts[np.argmin(s)]
    rect[2] = pts[np.argmax(s)]
    # The top-right point will have the smallest difference,
    # whereas the bottom-left will have the largest difference
    diff = np.diff(pts, axis=1)
    rect[1] = pts[np.argmin(diff)]
    rect[3] = pts[np.argmax(diff)]
    return rect

def four_point_transform(image, pts):
    # Obtain a consistent order of the points and unpack them
    rect = order_points(pts)
    (tl, tr, br, bl) = rect
    # Compute the width of the new image, which will be the
    # maximum distance between bottom-right and bottom-left
    # x-coordinates or the top-right and top-left x-coordinates
    widthA = np.sqrt(((br[0] - bl[0]) ** 2) + ((br[1] - bl[1]) ** 2))
    widthB = np.sqrt(((tr[0] - tl[0]) ** 2) + ((tr[1] - tl[1]) ** 2))
    maxWidth = max(int(widthA), int(widthB))
    # Compute the height of the new image, which will be the
    # maximum distance between the top-right and bottom-right
    # y-coordinates or the top-left and bottom-left y-coordinates
    heightA = np.sqrt(((tr[0] - br[0]) ** 2) + ((tr[1] - br[1]) ** 2))
    heightB = np.sqrt(((tl[0] - bl[0]) ** 2) + ((tl[1] - bl[1]) ** 2))
    maxHeight = max(int(heightA), int(heightB))
    # Construct the set of destination points to obtain a
    # "birds eye view" (i.e. top-down view) of the image
    dst = np.array([
        [0, 0],
        [maxWidth - 1, 0],
        [maxWidth - 1, maxHeight - 1],
        [0, maxHeight - 1]], dtype="float32")
    # Compute the perspective transform matrix and then apply it
    M = cv2.getPerspectiveTransform(rect, dst)
    warped = cv2.warpPerspective(image, M, (maxWidth, maxHeight))
    return warped

def straighten_image(image_path):
    # Load the image
    image = load_image(image_path)
    # Preprocess the image
    edged = preprocess_image(image)
    # Find the document contours
    doc_contour = find_document_contours(edged)
    if doc_contour is None:
        raise Exception("Document contour not found!")
    # Apply the four-point transform to get a top-down view of the document
    warped = four_point_transform(image, doc_contour.reshape(4, 2))
    return warped

# Example usage
image_path = 'camscanner_util/images/cropped_model01.jpeg'
straightened_image = straighten_image(image_path)

# Display the result
plt.imshow(cv2.cvtColor(straightened_image, cv2.COLOR_BGR2RGB))
plt.show()
