from src.app.utils.document_model import DocumentModel
from src.app.services.ocr_service import OCRService
from src.app.utils import constants, models_utils
import re
import logging


# Configure logging
logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')


def OCR_All_FOOTER(image_path, model=None, random_id=None, module='default'):
    """Extract and process footer data from the image"""

    # Initialize OCR service and document model
    ocr_service = OCRService()
    document_model = DocumentModel()

    # Get OCR results without row threshold
    ocr_result = ocr_service.extract_text(image_path, use_row_threshold=False)

    # Extract all numbers matching the specified patterns
    numbers = []

    for line in ocr_result.raw_lines:
        logging.info(f"\nLine Footer OCR Result: {line}")

        # Pattern to match numbers with various formats
        pattern = r'\b\d+[.,]?\s*\d*\b|\b\d{1,3}(?:[.,]\d{3})*(?:[.,]\d+)?\b'
        matches = re.finditer(pattern, line)

        for match in matches:
            num_str = match.group()
            logging.info(f"\nOriginal number found: {num_str}")

            # Clean and standardize the number format
            # Replace ', ' with '.'
            num_str = num_str.replace(', ', '.')
            # Replace '. ' with '.'
            num_str = num_str.replace('. ', '.')
            # Replace remaining ',' with '.'
            num_str = num_str.replace(',', '.')
            # Remove spaces
            num_str = num_str.replace(' ', '')

            logging.info(f"After cleaning: {num_str}")

            # Only process numbers with more than 4 characters
            if len(num_str) > 4:
                try:
                    number = float(num_str)
                    numbers.append(number)
                    logging.info(f"Converted to number: {number}")
                except ValueError as e:
                    logging.info(f"Conversion failed: {e}")
                    continue

    # Remove duplicates and sort numbers from smallest to largest
    numbers = sorted(list(set(numbers)))
    logging.info("\nAll extracted and sorted numbers (without duplicates):")
    logging.info(numbers)

    # Find which group the model belongs to
    model_group = None
    for group, models in constants.GROSSISTES_MODEL_TYPES.items():
        if model in models:
            model_group = group
            break

    # Initialize footer values with defaults
    document_model.footer.total_pph = "0.00"
    document_model.footer.total_ttc = "0.00"
    document_model.footer.total_ppv = "0.00"
    document_model.footer.cumul_periode = "0.00"

    # Only proceed with assigning values if numbers were found
    if numbers and model_group:
        # Define expected number of prices for each group
        expected_prices = {
            'SPR': 0,  # No prices
            'UGP_GROUP': 3,  # PPH/TTC, PPV, CUMUL_PERIODE
            'SOPHACENTRE_GROUP': 3,  # PPH/TTC, PPV, CUMUL_PERIODE
            'SOPHANORD_GROUP': 3,  # PPH/TTC, PPV, CUMUL_PERIODE
            'COOPER_GROUP': 2,  # PPH/TTC, CUMUL_PERIODE
            'GPM_GROUP': 3,  # PPH/TTC, PPV, CUMUL_PERIODE
            'SOREPHA_GROUP': 3,  # PPH/TTC, PPV, CUMUL_PERIODE
            'RECAMED_GROUP': 3,  # PPH/TTC, PPV, CUMUL_PERIODE
        }

        # Assign values based on the group type and available numbers
        if len(numbers) >= 1:
            document_model.footer.total_pph = f"{numbers[0]:.2f}"
            document_model.footer.total_ttc = f"{numbers[0]:.2f}"

        if model_group != 'COOPER_GROUP':  # For groups with PPV
            if len(numbers) >= 2:
                document_model.footer.total_ppv = f"{numbers[1]:.2f}"
            if len(numbers) >= 3:
                document_model.footer.cumul_periode = f"{numbers[2]:.2f}"
        else:  # For COOPER_GROUP (no PPV)
            if len(numbers) >= 2:
                document_model.footer.cumul_periode = f"{numbers[1]:.2f}"
    else:
        # Log warning if no numbers found or model group not recognized
        if not numbers:
            logging.warning(f"No numbers found in the footer image: {image_path}")
        if not model_group:
            logging.warning(f"Model {model} not found in any group")

    logging.info("\nFinal Results:")
    logging.info(f"Total PPH: {document_model.footer.total_pph}")
    logging.info(f"Total TTC: {document_model.footer.total_ttc}")
    logging.info(f"Total PPV: {document_model.footer.total_ppv}")
    logging.info(f"Cumul Periode: {document_model.footer.cumul_periode}")

    return document_model


""" -- ******* ---------------------------------------- Testing  --------------------------------------- ******* -- """

# if __name__ == "__main__":
#     logging.info("Starting OCR processing script...")
#
#     model_image_pairs = [
#         (constants.GPM, "temp_util/BL_footer_cropped_000_GPM__.JPG"),
#         # (constants.SOPHADIMS, "temp_util/BL_footer_cropped_000_SOPHADIMS__.JPG"),
#         # (constants.COOPER_PHARMA_CASA, "temp_util/BL_footer_cropped_000_COOPER__.JPG"),
#         # (constants.SOPHACA, "temp_util/BL_footer_cropped_000_SOPHACA__.JPG"),
#     ]
#
#     # Add error handling for file existence
#     import os
#
#     if not os.path.exists(model_image_pairs[0][1]):
#         logging.info(f"Error: Image file not found at {model_image_pairs[0][1]}")
#     else:
#         logging.info(f"Processing image: {model_image_pairs[0][1]}")
#         result = extract_footer_data(model_image_pairs[0][1])
#         logging.info("\nFinal Results:")
#         logging.info(f"Total PPH: {result.footer.total_pph}")
#         logging.info(f"Total TTC: {result.footer.total_ttc}")
#         logging.info(f"Total PPV: {result.footer.total_ppv}")
