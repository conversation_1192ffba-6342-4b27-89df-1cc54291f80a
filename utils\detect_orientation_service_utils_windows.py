import tkinter as tk
from tkinter import filedialog, messagebox
from PIL import Image, ImageTk
from doctr.models._utils import estimate_orientation
import cv2
import logging
import numpy as np


class ImageRotationApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Image Rotation Detector")
        self.root.geometry("800x600")

        # Main frame
        self.main_frame = tk.Frame(root)
        self.main_frame.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

        # Image display area
        self.image_label = tk.Label(self.main_frame, text="No image selected")
        self.image_label.pack(pady=10)

        # Buttons frame
        self.button_frame = tk.Frame(self.main_frame)
        self.button_frame.pack(pady=10)

        # Select image button
        self.select_btn = tk.Button(
            self.button_frame,
            text="Select Image",
            command=self.select_image
        )
        self.select_btn.pack(side=tk.LEFT, padx=5)

        # Process button
        self.process_btn = tk.Button(
            self.button_frame,
            text="Detect & Correct",
            command=self.process_image,
            state=tk.DISABLED
        )
        self.process_btn.pack(side=tk.LEFT, padx=5)

        # Result label
        self.result_label = tk.Label(self.main_frame, text="")
        self.result_label.pack(pady=10)

        # Initialize variables
        self.current_image_path = None
        self.original_image = None
        self.processed_image = None

    def select_image(self):
        file_path = filedialog.askopenfilename(
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.bmp *.tiff *.tif")
            ]
        )

        if file_path:
            self.current_image_path = file_path
            self.original_image = cv2.imread(file_path)
            self.display_image(self.original_image)
            self.process_btn.config(state=tk.NORMAL)
            self.result_label.config(text="")

    def process_image(self):
        try:
            # Detect orientation
            angle = estimate_orientation(
                self.original_image,
                n_ct=50,
                ratio_threshold_for_lines=5
            )

            # Rotate image
            self.processed_image = self._rotate_image(self.original_image, angle)

            # Display rotated image
            self.display_image(self.processed_image)

            # Update result label
            self.result_label.config(
                text=f"Detected angle (counter-clockwise): {angle:.2f} degrees"
            )

            # Ask user if they want to save the corrected image
            if messagebox.askyesno("Save Image", "Do you want to save the corrected image?"):
                self.save_image()

        except Exception as e:
            messagebox.showerror("Error", f"Error processing image: {str(e)}")

    def display_image(self, cv2_image):
        # Convert CV2 image to PIL format
        image_rgb = cv2.cvtColor(cv2_image, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(image_rgb)

        # Resize image to fit display area while maintaining aspect ratio
        display_size = (600, 400)
        pil_image.thumbnail(display_size, Image.Resampling.LANCZOS)

        # Convert to PhotoImage
        photo = ImageTk.PhotoImage(pil_image)

        # Update label
        self.image_label.config(image=photo)
        self.image_label.image = photo  # Keep a reference!

    def save_image(self):
        if self.processed_image is None:
            return

        file_path = filedialog.asksaveasfilename(
            defaultextension=".jpg",
            filetypes=[("JPEG files", "*.jpg"), ("All files", "*.*")]
        )

        if file_path:
            cv2.imwrite(file_path, self.processed_image)
            messagebox.showinfo("Success", "Image saved successfully!")

    def _rotate_image(self, image: np.ndarray, angle: float) -> np.ndarray:
        if abs(angle) < 0.5:
            return image

        (h, w) = image.shape[:2]
        center = (w // 2, h // 2)
        M = cv2.getRotationMatrix2D(center, -angle, 1.0)
        rotated = cv2.warpAffine(
            image,
            M,
            (w, h),
            flags=cv2.INTER_CUBIC,
            borderMode=cv2.BORDER_REPLICATE
        )
        return rotated


def main():
    root = tk.Tk()
    app = ImageRotationApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
