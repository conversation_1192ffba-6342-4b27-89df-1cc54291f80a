from fastapi import APIRouter, File, UploadFile, Form, HTTPException, WebSocketDisconnect, Depends, Header
from src.app.dependencies import get_current_user, check_headers
from fastapi.responses import JSONResponse
from src.app.camscanner_lite import smart_crop
from src.app.camscanner_lite.magic_pro_filter import detect_table_contour
import uuid
import traceback
import asyncio
import logging
import sys
from src.app.services.websocket_manager import manager
from src.app.utils.helpers import get_temp_path, pdf_image_async, zip_image_async
import cv2
import os
from concurrent.futures import ThreadPoolExecutor
from doctr.models._utils import estimate_orientation


router = APIRouter()

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Initialize a ThreadPoolExecutor
executor = ThreadPoolExecutor(max_workers=10)

def check_image_quality(image_path):
    result = {
        "status": "success",
        "processed_image": None,
        "messages": [],
        "user_messages": []  # New list for user-friendly messages
    }

    # Check file size
    file_size = os.path.getsize(image_path)
    min_size, max_size = 820000, 10000000
    if not (min_size <= file_size <= max_size):
        result["status"] = "error"
        result["messages"].append(
            f"Image size ({file_size} bytes) is not within acceptable range ({min_size} - {max_size} bytes)")
        result["user_messages"].append("La taille de l'image n'est pas adaptée. Veuillez utiliser une image entre 1 MB et 10 MB.")


    # Read image
    image = cv2.imread(image_path)
    if image is None:
        result["status"] = "error"
        result["messages"].append("Failed to read image file")
        result["user_messages"].append("Impossible de lire le fichier image. Veuillez réessayer avec une autre image.")
        return result

    # # Check resolution
    # height, width = image.shape[:2]
    # min_width, min_height = 1000, 1000
    # if not (width >= min_width and height >= min_height):
    #     result["status"] = "error"
    #     result["messages"].append(
    #         f"Image resolution ({width}x{height}) is below minimum required ({min_width}x{min_height})")
    #     result["user_messages"].append("La résolution de l'image est trop basse. Veuillez utiliser une image de meilleure qualité.")


    # Check contrast
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    contrast = cv2.meanStdDev(gray)[1][0][0]
    min_contrast = 39  # must be 40
    logging.info(f"Image contrast: {contrast:.2f}. Minimum expected: {min_contrast}")
    if contrast < min_contrast:
        result["status"] = "error"
        result["messages"].append(f"Image contrast ({contrast:.2f}) is below minimum required ({min_contrast})")
        result["user_messages"].append("Le contraste de l'image est trop faible. Veuillez prendre une photo avec un meilleur éclairage.")


    # Check lighting
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    brightness = hsv[:, :, 2].mean()
    min_brightness, max_brightness = 100, 250
    if not (min_brightness <= brightness <= max_brightness):
        result["status"] = "error"
        result["messages"].append(
            f"Image brightness ({brightness:.2f}) is not within acceptable range ({min_brightness} - {max_brightness})")
        result["user_messages"].append("La luminosité de l'image n'est pas optimale. Veuillez prendre une photo dans de meilleures conditions d'éclairage.")


    # Clean image
    cleaned_image = clean_image(image)

    # Check skew
    angle = check_orientation(image_path)
    if angle != 0:
        result["messages"].append(f"Image was rotated by {angle:.2f} degrees")
        result["user_messages"].append(f"L'image a été automatiquement tournée de {angle:.2f} degrés.")


    if result["status"] == "success":
        result["processed_image"] = cleaned_image
        result["messages"].append("Image preprocessing completed successfully")
        result["user_messages"].append("Le traitement de l'image a réussi.")

    # else:
    #     result["messages"].insert(0, "Please improve the image quality and try again")
    #     result["user_messages"].insert(0, "Veuillez améliorer la qualité de l'image et réessayer.")


    return result


# Helper functions (unchanged)
def clean_image(image):
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    denoised = cv2.fastNlMeansDenoising(gray)
    _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    return binary


# def check_orientation(image):
#     height, width = image.shape[:2]
#     return width > height

def check_orientation(image):
    """ -----------check skew----------- """
    img_read = cv2.imread(image)
    angle = detect_table_contour(img_read)
    logging.info(f"angle: {angle}")
    return angle


def check_rotation_angle(angle: float) -> bool:
    """
    Check if the rotation angle falls within specific ranges:
    - Between 45 and 135 degrees
    - Between -45 and -135 degrees
    - Between -1 and -10 degrees

    Args:
        angle (float): The detected rotation angle

    Returns:
        bool: True if angle is within specified ranges, False otherwise
    """
    # Define the ranges
    range1 = (45, 135)  # 45 to 135 degrees
    range2 = (-135, -45)  # -135 to -45 degrees
    range3 = (-10, -1)  # -10 to -1 degrees

    # Check if angle falls within any of these ranges
    in_range1 = range1[0] <= angle <= range1[1]
    in_range2 = range2[0] <= angle <= range2[1]
    in_range3 = range3[0] <= angle <= range3[1]

    return in_range1 or in_range2 or in_range3

@router.post("/")
async def smart_crop_endpoint(image: UploadFile = File(...), job_id: str = Form(...), isScanner: str = Form(...)):
    try:

        # Log the entire request in one line
        logging.info(f"Received request from process_image_supp_endpoint")
        logging.info(f"image: {image}")
        logging.info(f"job_id: {job_id}")
        logging.info(f"isScanner: {isScanner}")

        # Run `process_smart_crop` in ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            lambda: asyncio.run(smart_crop_process(image, job_id, isScanner))
        )

        # Return the result
        return result

    except HTTPException as http_exc:
        logger.error(f"Erreur HTTP: {http_exc.detail}")
        return JSONResponse(status_code=http_exc.status_code, content={"message": http_exc.detail})
    except Exception as e:
        logger.error(f"Erreur inattendue: {traceback.format_exc()}")
        return JSONResponse(status_code=500, content={"message": "Erreur interne du serveur"})


async def smart_crop_process(image: UploadFile = File(...), job_id: str = Form(...), isScanner: str = Form(...)):
    try:

        # Change the isScanner type to boolean
        isScanner = isScanner.lower() == "true"


        # Temporary directory for processed files
        output_dir_path = get_temp_path() / "smart_crop_output"
        output_dir_path.mkdir(exist_ok=True, parents=True)

        # Output of the origin image when the first service cropping
        origin_images_path = get_temp_path() / "origin_images_output"
        origin_images_path.mkdir(exist_ok=True, parents=True)

        # Generate a random UUID for the image filename
        random_id = str(uuid.uuid4())[:8]  # Extract the first 8 characters of the UUID

        # Notify the client about progress (20% complete)
        try:
            # logger.info(f"20% top ...")
            await manager.send_progress(job_id, 20)
            await asyncio.sleep(0.01)
            # logger.info(f"20% bottom ...")
        except (WebSocketDisconnect, RuntimeError) as e:
            logger.error(f"Error sending progress update: {e}")


        # """ Zip """
        # image_path = await zip_image_async(output_dir_path, origin_images_path, random_id, image)
        # """ Zip """

        """ PDF """
        image_path = await pdf_image_async(output_dir_path, origin_images_path, random_id, image, isScanner=isScanner)
        """ PDF """


        """ Check the image quality """
        # result = check_image_quality(str(image_path))
        # if result["status"] == "error":
        #     return JSONResponse(status_code=400, content={"status": "error", "user_messages": result["user_messages"]})
        """ Check the image quality """

        # Notify the client about progress (50% complete)
        try:
            await manager.send_progress(job_id, 50)
            await asyncio.sleep(0.01)
        except (WebSocketDisconnect, RuntimeError) as e:
            logger.error(f"Error sending progress update: {e}")

        logging.info(f"isScanner __ 1 : {isScanner}")
        logging.info(f"isScanner __ 1 : {isScanner}")

        # Process the image to detect document corners
        scanner = smart_crop.DocScanner(interactive=True, random_id=random_id, output_dir=output_dir_path)
        coordinates = scanner.scan(image_path, isScanner)

        # Ensure that coordinates were found
        if not coordinates:
            raise HTTPException(status_code=404, detail="Aucun coin de document trouvé dans l'image.")

        # Detect the angle
        orig_img = cv2.imread(str(image_path), cv2.IMREAD_COLOR)
        angle = estimate_orientation(orig_img, n_ct=50, ratio_threshold_for_lines=5)

        # Check if rotation is needed
        needs_rotation = check_rotation_angle(angle)

        # Notify the client about progress (100% complete)
        try:
            await manager.send_progress(job_id, 100)
            await asyncio.sleep(0.01)
        except (WebSocketDisconnect, RuntimeError) as e:
            logger.error(f"Error sending progress update: {e}")

        # Return the coordinates as JSON
        data = {
            "filename": image.filename,
            "coordinates": coordinates,
            "uuid": random_id,
            "needs_rotation": needs_rotation,
        }
        return data

    except HTTPException as http_exc:
        logger.error(f"Erreur HTTP: {http_exc.detail}")
        return JSONResponse(status_code=http_exc.status_code, content={"message": http_exc.detail})
    except Exception as e:
        logger.error(f"Erreur inattendue: {traceback.format_exc()}")
        return JSONResponse(status_code=500, content={"message": "Erreur interne du serveur"})
