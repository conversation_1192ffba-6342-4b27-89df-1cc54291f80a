import sqlite3
import json
from pathlib import Path
import logging

DATABASE_NAME = Path(__file__).parents[2] / "database/ocr_document_grossiste.db"
logging.info( f"Database :  {DATABASE_NAME}")

def get_db_connection():
    conn = sqlite3.connect(DATABASE_NAME)
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    conn = get_db_connection()
    c = conn.cursor()
    try:
        c.execute('''CREATE TABLE IF NOT EXISTS pre_bl_ocr
                             (ID_BL INTEGER PRIMARY KEY AUTOINCREMENT,
                              Content JSON,
                              ID_USER TEXT,
                              status TEXT DEFAULT 'EN_ATTENTE',
                              ID_TENANT TEXT,
                              CODE_TENANT TEXT,
                              date TEXT DEFAULT CURRENT_TIMESTAMP,
                              id_BL_origine TEXT DEFAULT NULL,
                              date_BL_origine TEXT DEFAULT NULL,
                              supplier_name TEXT DEFAULT NULL,
                              supplier_id TEXT DEFAULT NULL, 
                              random_id TEXT DEFAULT NULL 
                              )''')
        conn.commit()
    except sqlite3.Error as e:
        logging.info(f"An error occurred: {e}")
    finally:
        conn.close()

def save_response_to_db(responses, id_user, tenant_code, tenant_id, random_id):
    conn = get_db_connection()
    c = conn.cursor()
    try:
        c.execute("BEGIN TRANSACTION")

        # Extract only the 'data' part from each response
        data_to_save = [response['data'] for response in responses if response.get('success') and response.get('data')]

        c.execute("INSERT INTO pre_bl_ocr (Content, ID_USER, ID_TENANT, CODE_TENANT, random_id) VALUES (json(?), ?, ?, ?, ?)",
                  (json.dumps(data_to_save), id_user, tenant_id, tenant_code, random_id))
        c.execute("SELECT last_insert_rowid()")
        id_bl = c.fetchone()[0]
        c.execute("COMMIT")

        logging.info(f"Saved response data to database with ID: {id_bl}")
    except sqlite3.Error as e:
        c.execute("ROLLBACK")
        logging.error(f"SQLite error in save_response_to_db: {e}")
        logging.error(f"Data to save: {data_to_save}")
        logging.error(f"ID_USER: {id_user}")
        logging.error(f"ID_TENANT: {tenant_id}")
        logging.error(f"CODE_TENANT: {tenant_code}"),

        raise
    finally:
        conn.close()
    return id_bl


def get_all_pre_bl_ocr(user_id: str, tenant_id: str):
    conn = get_db_connection()
    c = conn.cursor()
    # Order by the 'date' column in descending order
    c.execute("""
        SELECT * 
        FROM pre_bl_ocr 
        WHERE status in ('EN_COURS', 'VALIDER') 
          AND ID_USER = ? 
          AND ID_TENANT = ? 
        ORDER BY date DESC
    """, (user_id, tenant_id))
    rows = c.fetchall()
    conn.close()

    results = []
    for row in rows:
        results.append({
            "ID_BL": row['ID_BL'],
            "Content": json.loads(row['Content']),
            "ID_USER": row['ID_USER'],
            "status": row['status'],
            "ID_TENANT": row['ID_TENANT'],
            "CODE_TENANT": row['CODE_TENANT'],
            "date": row['date'],
            "id_BL_origine": row['id_BL_origine'],
            "date_BL_origine": row['date_BL_origine'],
            "supplier_name": row['supplier_name'],
            "supplier_id": str(row['supplier_id']),
            "random_id": row['random_id']
        })

    return results


def get_pre_bl_ocr_by_id(id_bl: int, user_id: str, tenant_id: str):
    conn = get_db_connection()
    c = conn.cursor()
    c.execute("SELECT * FROM pre_bl_ocr WHERE ID_BL = ? AND status in ('EN_COURS', 'VALIDER')  AND ID_USER = ? AND ID_TENANT = ?", (id_bl, user_id, tenant_id))
    row = c.fetchone()
    conn.close()

    if row:
        logging.info(dict(row))  # Convert sqlite3.Row to dictionary and print
        return {
            "ID_BL": row['ID_BL'],
            "Content": json.loads(row['Content']),
            "ID_USER": row['ID_USER'],
            "status": row['status'],
            "ID_TENANT": row['ID_TENANT'],
            "CODE_TENANT": row['CODE_TENANT'],
            "date": row['date'],
            "id_BL_origine": row['id_BL_origine'],
            "date_BL_origine": row['date_BL_origine'],
            "supplier_name": row['supplier_name'],
            "supplier_id": str(row['supplier_id']),
            "random_id": row['random_id']

        }
    else:
        return None


def get_pre_bl_ocr_by_random_id(random_id: str, user_id: str, tenant_id: str):
    conn = get_db_connection()
    c = conn.cursor()
    c.execute("SELECT * FROM pre_bl_ocr WHERE random_id = ? AND status in ('EN_COURS', 'VALIDER')  AND ID_USER = ? AND ID_TENANT = ?", (random_id, user_id, tenant_id))
    row = c.fetchone()
    conn.close()

    if row:
        logging.info(dict(row))  # Convert sqlite3.Row to dictionary and print
        return {
            "ID_BL": row['ID_BL'],
            "Content": json.loads(row['Content']),
            "ID_USER": row['ID_USER'],
            "status": row['status'],
            "ID_TENANT": row['ID_TENANT'],
            "CODE_TENANT": row['CODE_TENANT'],
            "date": row['date'],
            "id_BL_origine": row['id_BL_origine'],
            "date_BL_origine": row['date_BL_origine'],
            "supplier_name": row['supplier_name'],
            "supplier_id": str(row['supplier_id']),
            "random_id": row['random_id']

        }
    else:
        return None


def get_all_pre_bl_ocr_by_user(user_id: int):
    conn = get_db_connection()
    c = conn.cursor()
    c.execute("SELECT * FROM pre_bl_ocr WHERE status in ('EN_COURS', 'VALIDER')  AND ID_USER = ?", (user_id,))
    rows = c.fetchall()
    conn.close()

    results = []
    for row in rows:
        results.append({
            "ID_BL": row['ID_BL'],
            "Content": json.loads(row['Content']),
            "ID_USER": row['ID_USER'],
            "status": row['status'],
            "ID_TENANT": row['ID_TENANT'],
            "CODE_TENANT": row['CODE_TENANT'],
            "date": row['date'],
            "id_BL_origine": row['id_BL_origine'],
            "date_BL_origine": row['date_BL_origine'],
            "supplier_name": row['supplier_name'],
            "supplier_id": str(row['supplier_id']),
            "random_id": row['random_id']

        })

    return results


def update_bl_status(bl_id, new_status, id_BL_origine, date_BL_origine, supplier_name, supplier_id):
    conn = get_db_connection()
    c = conn.cursor()
    try:
        c.execute("UPDATE pre_bl_ocr SET status = ?, id_BL_origine = ?, date_BL_origine = ?, supplier_name = ?, supplier_id = ? WHERE ID_BL = ?",
                  (new_status, id_BL_origine, date_BL_origine, supplier_name, supplier_id, bl_id))
        conn.commit()
        return c.rowcount > 0
    except sqlite3.Error as e:
        logging.info(f"An error occurred: {e}")
        return False
    finally:
        conn.close()


def update_bl_status_valider(bl_id: int, user_id: str, tenant_id: str):
    conn = get_db_connection()
    c = conn.cursor()
    try:
        c.execute("UPDATE pre_bl_ocr SET status = 'VALIDER' WHERE ID_BL = ? AND ID_USER = ? AND ID_TENANT = ?",
                  (bl_id, user_id, tenant_id))
        conn.commit()
        return c.rowcount > 0
    except sqlite3.Error as e:
        logging.info(f"An error occurred: {e}")
        return False
    finally:
        conn.close()