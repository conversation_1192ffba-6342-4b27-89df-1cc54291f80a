### Token_Classification.py
from transformers import AutoTokenizer, AutoModelForTokenClassification
from transformers import pipeline
import logging
import traceback

def classify_tokens(text):
    """Use a pre-trained token classification model to classify tokens."""
    try:
        model_name = "dbmdz/bert-large-cased-finetuned-conll03-english"
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForTokenClassification.from_pretrained(model_name)

        nlp_pipeline = pipeline("token-classification", model=model, tokenizer=tokenizer)

        results = nlp_pipeline(text)

        for result in results:
            logging.info(f"Entity: {result['entity']}, Word: {result['word']}, Score: {result['score']}, Start: {result['start']}, End: {result['end']}")

        return results
    except Exception as e:
        logging.error(f"Error in classify_tokens: {traceback.format_exc()}")
        raise e

# Example usage
if __name__ == "__main__":
    input_text = "Designation: Paracetamol 500mg, Quantity: 100, Price: 10.50"
    token_classification = classify_tokens(input_text)
    logging.info(token_classification)
