import logging

from src.app.camscanner_lite.deeplab_service import load_deeplab_model, deep_scan_cv2
import torch
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
import numpy as np
import cv2
import logging

router = APIRouter()

class RealtimeContoursManager:
    def __init__(self):
        self.active_connections = {}
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.deep_model = load_deeplab_model(model_name="r50", device=self.device)

    async def process_frame(self, img):
        final_doc, corners_4 = deep_scan_cv2(
            image=img,
            trained_model=self.deep_model,
            device=self.device,
            image_size=384
        )
        if corners_4 is not None:
            return corners_4.reshape(-1, 2).astype(int).tolist()
        return None


# Update WebSocket handler
@router.websocket("/websocket/realtime_processing")
async def realtime_contours(websocket: WebSocket):
    manager = RealtimeContoursManager()
    await websocket.accept()

    try:
        while True:
            data = await websocket.receive_bytes()
            nparr = np.frombuffer(data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            corners = await manager.process_frame(img)
            if corners:
                await websocket.send_json({"contour": corners})
            else:
                await websocket.send_json({"error": "No document found"})

    except WebSocketDisconnect:
        manager.disconnect(websocket)
        logging.INFO("WebSocket connection closed")