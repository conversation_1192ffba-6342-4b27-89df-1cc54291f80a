import traceback
import logging

import cv2
from src.app.services import pre_processing
import numpy as np
import os
from src.app.ocr import header
from src.app.camscanner_lite import magic_pro_filter


# output_dir_path_identify_supplier = Path("../data/identify_suppliers")

# Function to process an image and extract the header and table
def process_image_crop(image_path, output_dir_path_param, model, random_id):
    """
    Process and crop an image to extract the header portion.

    Args:
        image_path: Path to the input image
        output_dir_path_param: Output directory path
        model: Model name for threshold selection
        random_id: Random identifier for the output file

    Returns:
        str: Path to the processed header image
    """
    try:
        # Ensure the output directory exists
        output_dir_path = output_dir_path_param
        output_dir_path.mkdir(parents=True, exist_ok=True)

        # Read the image
        image = cv2.imread(str(image_path))

        if image is None:
            logging.error(f"Failed to read image from path: {image_path}")
            raise ValueError(f"Image could not be read from path: {image_path}")

        # Log image dimensions
        logging.info(f"Image dimensions: {image.shape}")

        # Define threshold values for different modules
        module_thresholds = {
            'SOPHACA': 215,
            'GLOBAL': 190,
            'SPR': 190,
            'SOPHADIMS': 190,
            'GPM': 190,
            'RECAMED': 143,
            'COOPER_PHARMA_CASA': 190
        }

        # Get the threshold value for the specified module
        thresh_value = module_thresholds.get(model, 190)
        logging.info(f"Using threshold value: {thresh_value} for model: {model}")

        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Apply threshold
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_TOZERO_INV)

        # Dilate the image
        kernel = np.ones((1, 1), np.uint8)
        dilated = cv2.dilate(binary, kernel, iterations=1)

        # Find contours
        contours, _ = cv2.findContours(dilated.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            logging.error("No contours found in the image")
            # If no contours found, return the original image
            header_image_filename = f"bl_identify_supplier_{random_id}__.jpg"
            header_image_path = os.path.join(output_dir_path, header_image_filename)
            cv2.imwrite(header_image_path, image)
            return header_image_path

        # Find the largest contour
        largest_area = 0
        largest_contour = None
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            area = w * h
            if area > largest_area:
                largest_area = area
                largest_contour = contour

        logging.info(f"Largest contour area: {largest_area}")

        # Construct the processed image filename
        header_image_filename = f"bl_identify_supplier_{random_id}__.jpg"
        header_image_path = os.path.join(output_dir_path, header_image_filename)

        # Process and save the header image
        if largest_contour is not None:
            x, y, w, h = cv2.boundingRect(largest_contour)

            # Ensure y is within valid range
            y = max(0, min(y, image.shape[0]))

            # Crop the header image
            header_image = image[0:y, 0:image.shape[1]]

            if header_image is None or header_image.size == 0:
                logging.warning("Header image is empty, using original image")
                cv2.imwrite(header_image_path, image)
            else:
                logging.info(f"Saving header image with dimensions: {header_image.shape}")
                cv2.imwrite(header_image_path, header_image)

            return header_image_path
        else:
            logging.warning("No valid contour found, using original image")
            cv2.imwrite(header_image_path, image)
            return header_image_path

    except Exception as e:
        logging.error(f"Error in process_image_crop: {str(e)}")
        logging.error(traceback.format_exc())
        raise


def apply_preprocessing(image_path, model):
    image = cv2.imread(str(image_path))

    if image is None:
        logging.info("Error: Image could not be read.")
        return None, None

    # Apply the preprocessing functions to the image
    preprocessed_image = pre_processing.processing_the_img(image, model, 'header')

    # Save the processed image in the new folder with the modified name
    processed_image_path = image_path.split('__.')[0] + '_processed.jpg'
    cv2.imwrite(processed_image_path, preprocessed_image)

    return processed_image_path

    # Show the processed image
    # plt.imshow(cv2.cvtColor(preprocessed_image, cv2.COLOR_BGR2RGB))
    # plt.show()


def identify_supplier(image_path, model, output_dir_path_identify_supplier, random_id, module='default'):
    try:
        # Filter the image ('Global')
        # if model == 'GLOBAL':
        #     image_name_output = image_path.stem + '_Filtered_' + random_id + image_path.suffix
        #     output_path = output_dir_path_identify_supplier / f"{image_name_output}"
        #
        #     image_path, _ = magic_pro_filter.apply_magic_pro_filter(str(image_path),
        #                                                             str(output_path),
        #                                                             'GLOBAL', 'custom')

        # Function to process an image and extract the header
        process_image_cropped = process_image_crop(image_path, output_dir_path_identify_supplier, model, random_id)

        if process_image_cropped is None:
            raise ValueError("Processed image crop returned None or header image is empty")

        if model != 'GLOBAL' and model != 'AUTRE':
            # Apply the preprocessing functions to the image
            apply_preprocessed = apply_preprocessing(process_image_cropped, model)
        else:
            apply_preprocessed = process_image_cropped.split('.')[0] + 'processed.jpg'
            cv2.imwrite(apply_preprocessed, cv2.imread(process_image_cropped))

        # Export Data of Supplier of the Header image
        document_model_header_supplier = header.OCR_Supplier_Info(apply_preprocessed, model, module, random_id)

        return document_model_header_supplier

    except FileNotFoundError as fnf_error:
        logging.error(f"Erreur de fichier: {fnf_error}")
        raise fnf_error
    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode identify_supplier: {traceback.format_exc()}")
        raise e

# random_id = str(uuid.uuid4())[:8]  # Extract the first 8 characters of the UUID
# document_header = identify_supplier('../data/origin_BL/BLS_1.jpg', constants.GPM, output_dir_path_identify_supplier, random_id)
#
# logging.info('header : ', document_header.header.name_fournisseur.upper())
