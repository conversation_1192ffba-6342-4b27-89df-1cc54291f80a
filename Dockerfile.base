FROM python:3.11-slim

RUN apt-get update && apt install -y tesseract-ocr

WORKDIR /app

COPY requirements.txt .

# install curl
RUN apt-get install -y curl


RUN pip install --upgrade pip

RUN pip install Cython

RUN pip install -r requirements.txt

RUN apt-get install -y libgl1-mesa-glx

RUN pip install python-dotenv

RUN pip install pymupdf

RUN pip install PyJWT


EXPOSE 8000








CMD ["uvicorn", "src.api:app", "--host", "0.0.0.0", "--port", "8000"]