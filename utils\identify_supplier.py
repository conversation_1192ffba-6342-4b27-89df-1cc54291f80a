import traceback
import logging

import cv2
from src.app.services import pre_processing
import numpy as np
import os
from src.app.ocr import header
from src.app.camscanner_lite import magic_pro_filter


# output_dir_path_identify_supplier = Path("../data/identify_suppliers")


def check_image_quality(image):
    """Check if image meets minimum quality requirements"""
    try:
        # Check image size
        if image.shape[0] < 100 or image.shape[1] < 100:
            logging.warning("Image too small")
            return False
            
        # Check image contrast
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        contrast = gray.std()
        if contrast < 20:  # Minimum contrast threshold
            logging.warning("Image contrast too low")
            return False
            
        # Check if image is too bright or too dark
        mean_brightness = np.mean(gray)
        if mean_brightness < 30 or mean_brightness > 225:
            logging.warning("Image too bright or too dark")
            return False
            
        return True
    except Exception as e:
        logging.error(f"Error checking image quality: {str(e)}")
        return False
    
# Function to process an image and extract the header and table
def process_image_crop(image_path, output_dir_path_param, model, random_id):
    try:
        # Input validation
        if not os.path.exists(str(image_path)):
            logging.error(f"Image path does not exist: {image_path}")
            return None

        # Ensure output directory exists
        output_dir_path = output_dir_path_param
        output_dir_path.mkdir(parents=True, exist_ok=True)

        # Read image
        image = cv2.imread(str(image_path))
        if image is None or image.size == 0:
            logging.error("Invalid image or empty image data")
            return None

        # Check image quality
        if not check_image_quality(image):
            logging.warning("Image quality check failed, attempting to process anyway")

        # Module-specific thresholds
        module_thresholds = {
            'SOPHACA': 215,
            'GLOBAL': 190,
            'SPR': 190,
            'SOPHADIMS': 190,
            'GPM': 190,
            'RECAMED': 143,
            'COOPER_PHARMA_CASA': 190
        }

        # Image processing
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        thresh_value = module_thresholds.get(model, 190)
        
        # Try different thresholding methods
        _, binary = cv2.threshold(gray, thresh_value, 255, cv2.THRESH_BINARY_INV)
        kernel = np.ones((1, 1), np.uint8)
        dilated = cv2.dilate(binary, kernel, iterations=1)
        contours, _ = cv2.findContours(dilated.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # If no contours found, try adaptive thresholding
        if not contours:
            binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY_INV, 11, 2)
            dilated = cv2.dilate(binary, kernel, iterations=1)
            contours, _ = cv2.findContours(dilated.copy(), cv2.RETR_EXTERNAL, 
                                         cv2.CHAIN_APPROX_SIMPLE)

        # Find largest contour
        largest_area = 0
        largest_contour = None
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > largest_area:
                largest_area = area
                largest_contour = contour

        # Generate output filename
        header_image_filename = f"bl_identify_supplier_{random_id}__.jpg"
        header_image_path = os.path.join(output_dir_path, header_image_filename)

        # Process and save image
        if largest_contour is not None:
            x, y, w, h = cv2.boundingRect(largest_contour)
            # Ensure reasonable crop area
            if h > 10 and w > 10:  # Minimum size threshold
                header_image = image[0:y, 0:image.shape[1]]
                if header_image is not None and header_image.size > 0:
                    cv2.imwrite(header_image_path, header_image)
                    return header_image_path

        # Fallback: save original image if processing fails
        logging.warning("Using original image as fallback")
        cv2.imwrite(header_image_path, image)
        return header_image_path

    except Exception as e:
        logging.error(f"Error in process_image_crop: {str(e)}")
        return None


def apply_preprocessing(image_path, model):
    try:
        if not os.path.exists(str(image_path)):
            logging.error(f"Image path does not exist: {image_path}")
            return None

        image = cv2.imread(str(image_path))
        if image is None:
            logging.error("Failed to read image")
            return None

        # Apply preprocessing
        preprocessed_image = pre_processing.processing_the_img(image, model, 'header')
        if preprocessed_image is None:
            logging.error("Preprocessing failed")
            return None

        # Save processed image
        processed_image_path = image_path.split('__.')[0] + '_processed.jpg'
        cv2.imwrite(processed_image_path, preprocessed_image)
        return processed_image_path

    except Exception as e:
        logging.error(f"Error in apply_preprocessing: {str(e)}")
        return None

    # Show the processed image
    # plt.imshow(cv2.cvtColor(preprocessed_image, cv2.COLOR_BGR2RGB))
    # plt.show()


def identify_supplier(image_path, model, output_dir_path_identify_supplier, random_id, module='default'):
    try:
        # Input validation
        if not isinstance(image_path, (str, Path)):
            raise ValueError("Invalid image path type")
        
        # Convert to Path object if string
        image_path = Path(image_path) if isinstance(image_path, str) else image_path
        
        # Ensure output directory exists
        output_dir_path_identify_supplier = Path(output_dir_path_identify_supplier)
        output_dir_path_identify_supplier.mkdir(parents=True, exist_ok=True)

        # Apply filters for GLOBAL model
        if model == 'GLOBAL':
            image_name_output = image_path.stem + '_Filtered_' + random_id + image_path.suffix
            output_path = output_dir_path_identify_supplier / f"{image_name_output}"
            
            try:
                image_path, _ = magic_pro_filter.apply_magic_pro_filter(
                    str(image_path),
                    str(output_path),
                    'GLOBAL',
                    'custom'
                )
            except Exception as e:
                logging.error(f"Filter application failed: {str(e)}")
                # Continue with original image if filtering fails
                image_path = str(image_path)

        # Process image with retry mechanism
        max_retries = 3
        processed_image = None
        
        for attempt in range(max_retries):
            try:
                processed_image = process_image_crop(
                    image_path, 
                    output_dir_path_identify_supplier,
                    model,
                    random_id
                )
                if processed_image is not None:
                    break
                logging.warning(f"Retry attempt {attempt + 1} of {max_retries}")
            except Exception as e:
                logging.error(f"Attempt {attempt + 1} failed: {str(e)}")
                if attempt == max_retries - 1:
                    raise

        if processed_image is None:
            raise ValueError("Failed to process image after multiple attempts")

        # Apply preprocessing based on model
        if model != 'GLOBAL':
            apply_preprocessed = apply_preprocessing(processed_image, model)
        else:
            apply_preprocessed = processed_image.split('.')[0] + 'processed.jpg'
            cv2.imwrite(apply_preprocessed, cv2.imread(processed_image))

        # Extract supplier information
        document_model_header_supplier = header.OCR_Supplier_Info(
            apply_preprocessed,
            model,
            module,
            random_id
        )

        return document_model_header_supplier

    except FileNotFoundError as fnf_error:
        logging.error(f"File not found error: {fnf_error}")
        raise
    except Exception as e:
        logging.error(f"Error in identify_supplier: {traceback.format_exc()}")
        raise

# random_id = str(uuid.uuid4())[:8]  # Extract the first 8 characters of the UUID
# output_dir_path_identify_supplier = Path("../data/identify_suppliers")
document_header = identify_supplier('temp_util/cropped_442b11c7_Filtered_442b11c7.jpg', 'GLOBAL', "output/", "442b11c7")

logging.info('header : ', document_header.header.name_fournisseur.upper())
