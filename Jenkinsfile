pipeline {
    agent any
    parameters {
        string(name: 'IMAGE_VERSION', defaultValue: '1', description: 'Version for the new Docker image')
        string(name: 'IMAGE_NAME', defaultValue: 'ocr_base', description: 'Name of the new Docker image')
    }
    environment {
       OCR_API_IMAGE = 'ocr_api'
    }

    stages {

        // delete old container after stopping it
        stage('stop and delete container'){
            steps {
                script {
                    sh "docker stop ocr_api || true"
                    sh "docker rm ocr_api || true"
                }
            }
        }


        stage('Clean Up Old Images') {
            steps {
                script {
                    // Remove images that start with 'winproduits-delta:V'
                    sh """
                        docker images -q '${OCR_API_IMAGE}:v*' | xargs -r docker rmi -f
                    """
                }
            }
        }
        stage('Build New Image') {
            steps {
                script {
                    def version = params.IMAGE_VERSION
                    // Build the new Docker image
                    sh "docker build -t ${OCR_API_IMAGE}:v${version} . --build-arg VERSION=${version}"
                }
            }
        }


        stage('Start New Container') {
                steps {
                    script {
                        def version = params.IMAGE_VERSION
                        def containerName = 'ocr_api'
                        // Start the new container with volume mount
                        sh """
                            docker run -d  --network=backend.prod --name ${containerName} ${OCR_API_IMAGE}:v${version} 
                        """
                    }
                }
        }
        }
}