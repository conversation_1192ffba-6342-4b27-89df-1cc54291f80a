[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
absl-py = "==2.1.0"
appdirs = "==1.4.4"
archspec = {file = "..\\..\\..\\..\\..\\..\\croot\\archspec_1709217642129\\work"}
argon2-cffi = {file = "..\\..\\..\\..\\..\\..\\opt\\conda\\conda-bld\\argon2-cffi_1645000214183\\work"}
argon2-cffi-bindings = {file = "..\\..\\..\\..\\..\\..\\ci_311\\argon2-cffi-bindings_1676424443321\\work"}
arrow = {file = "..\\..\\..\\..\\..\\..\\ci_311\\arrow_1678249767083\\work"}
astroid = {file = "..\\..\\..\\..\\..\\..\\ci_311\\astroid_1678740610167\\work"}
astropy = {file = "..\\..\\..\\..\\..\\..\\b\\abs_2fb3x_tapx\\croot\\astropy_1697468987983\\work"}
asttokens = {file = "..\\..\\..\\..\\..\\..\\opt\\conda\\conda-bld\\asttokens_1646925590279\\work"}
astunparse = "==1.6.3"
async-lru = {file = "..\\..\\..\\..\\..\\..\\b\\abs_e0hjkvwwb5\\croot\\async-lru_1699554572212\\work"}
atomicwrites = "==1.4.0"
attrs = {file = "..\\..\\..\\..\\..\\..\\b\\abs_35n0jusce8\\croot\\attrs_1695717880170\\work"}
automat = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\automat_1600298431173\\work"}
autopep8 = {file = "..\\..\\..\\..\\..\\..\\opt\\conda\\conda-bld\\autopep8_1650463822033\\work"}
babel = {file = "..\\..\\..\\..\\..\\..\\ci_311\\babel_1676427169844\\work"}
backcall = "==0.2.0"
"backports.functools-lru-cache" = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\backports.functools_lru_cache_1618170165463\\work"}
"backports.tempfile" = {file = "..\\..\\..\\..\\..\\..\\home\\linux1\\recipes\\ci\\backports.tempfile_1610991236607\\work"}
"backports.weakref" = "==1.0.post1"
bcrypt = {file = "..\\..\\..\\..\\..\\..\\ci_311\\bcrypt_1676435170049\\work"}
beautifulsoup4 = {file = "..\\..\\..\\..\\..\\..\\b\\abs_0agyz1wsr4\\croot\\beautifulsoup4-split_1681493048687\\work"}
binaryornot = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\binaryornot_1617751525010\\work"}
black = {file = "..\\..\\..\\..\\..\\..\\b\\abs_29gqa9a44y\\croot\\black_1701097690150\\work"}
bleach = {file = "..\\..\\..\\..\\..\\..\\opt\\conda\\conda-bld\\bleach_1641577558959\\work"}
blinker = {file = "..\\..\\..\\..\\..\\..\\b\\abs_d9y2dm7cw2\\croot\\blinker_1696539752170\\work"}
blis = "==0.7.11"
bokeh = {file = "..\\..\\..\\..\\..\\..\\b\\abs_74ungdyhwc\\croot\\bokeh_1706912192007\\work"}
boltons = {file = "..\\..\\..\\..\\..\\..\\ci_311\\boltons_1677729932371\\work"}
bottleneck = {file = "..\\..\\..\\..\\..\\..\\b\\abs_f05kqh7yvj\\croot\\bottleneck_1707864273291\\work"}
brotli = {file = "..\\..\\..\\..\\..\\..\\ci_311\\brotli-split_1676435766766\\work"}
cachetools = "==5.3.3"
catalogue = "==2.0.10"
certifi = "==2024.2.2"
cffi = {file = "..\\..\\..\\..\\..\\..\\b\\abs_924gv1kxzj\\croot\\cffi_1700254355075\\work"}
chardet = {file = "..\\..\\..\\..\\..\\..\\ci_311\\chardet_1676436134885\\work"}
charset-normalizer = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\charset-normalizer_1630003229654\\work"}
click = {file = "..\\..\\..\\..\\..\\..\\b\\abs_f9ihnt72pu\\croot\\click_1698129847492\\work"}
cloudpathlib = "==0.16.0"
cloudpickle = {file = "..\\..\\..\\..\\..\\..\\b\\abs_3796yxesic\\croot\\cloudpickle_1683040098851\\work"}
clyent = "==1.2.2"
colorama = {file = "..\\..\\..\\..\\..\\..\\ci_311\\colorama_1676422310965\\work"}
colorcet = {file = "..\\..\\..\\..\\..\\..\\ci_311\\colorcet_1676440389947\\work"}
comm = {file = "..\\..\\..\\..\\..\\..\\ci_311\\comm_1678376562840\\work"}
conda = {file = "..\\..\\..\\..\\..\\..\\b\\abs_f3egtzumcq\\croot\\conda_1715635886039\\work"}
conda-build = {file = "..\\..\\..\\..\\..\\..\\b\\abs_3ed9gavxgz\\croot\\conda-build_1708025907525\\work"}
conda-content-trust = {file = "..\\..\\..\\..\\..\\..\\b\\abs_e3bcpyv7sw\\croot\\conda-content-trust_1693490654398\\work"}
conda-libmamba-solver = {file = "..\\..\\..\\..\\..\\..\\croot\\conda-libmamba-solver_1706733287605\\work\\src"}
conda-pack = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\conda-pack_1611163042455\\work"}
conda-package-handling = {file = "..\\..\\..\\..\\..\\..\\b\\abs_b9wp3lr1gn\\croot\\conda-package-handling_1691008700066\\work"}
conda-token = {file = "..\\..\\..\\..\\..\\paulyim\\miniconda3\\envs\\c3i\\conda-bld\\conda-token_1662660369760\\work"}
conda-verify = "==3.4.2"
conda-index = {file = "..\\..\\..\\..\\..\\..\\croot\\conda-index_1706633791028\\work"}
conda-package-streaming = {file = "..\\..\\..\\..\\..\\..\\b\\abs_6c28n38aaj\\croot\\conda-package-streaming_1690988019210\\work"}
confection = "==0.1.4"
constantly = {file = "..\\..\\..\\..\\..\\..\\b\\abs_cbuavw4443\\croot\\constantly_1703165617403\\work"}
contourpy = {file = "..\\..\\..\\..\\..\\..\\b\\abs_853rfy8zse\\croot\\contourpy_1700583617587\\work"}
cookiecutter = {file = "..\\..\\..\\..\\..\\..\\b\\abs_3d1730toam\\croot\\cookiecutter_1700677089156\\work"}
cryptography = {file = "..\\..\\..\\..\\..\\..\\b\\abs_531eqmhgsd\\croot\\cryptography_1707523768330\\work"}
cssselect = {file = "..\\..\\..\\..\\..\\..\\b\\abs_71gnjab7b0\\croot\\cssselect_1707339955530\\work"}
cssselect2 = "==0.7.0"
cycler = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\cycler_1637851556182\\work"}
cymem = "==2.0.8"
cytoolz = {file = "..\\..\\..\\..\\..\\..\\b\\abs_d43s8lnb60\\croot\\cytoolz_1701723636699\\work"}
dask = {file = "..\\..\\..\\..\\..\\..\\b\\abs_1899k8plyj\\croot\\dask-core_1701396135885\\work"}
datashader = {file = "..\\..\\..\\..\\..\\..\\b\\abs_cb5s63ty8z\\croot\\datashader_1699544282143\\work"}
debugpy = {file = "..\\..\\..\\..\\..\\..\\b\\abs_c0y1fjipt2\\croot\\debugpy_1690906864587\\work"}
decorator = {file = "..\\..\\..\\..\\..\\..\\opt\\conda\\conda-bld\\decorator_1643638310831\\work"}
defusedxml = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\defusedxml_1615228127516\\work"}
diff-match-patch = {file = "..\\..\\..\\..\\..\\ktietz\\demo\\mc3\\conda-bld\\diff-match-patch_1630511840874\\work"}
dill = {file = "..\\..\\..\\..\\..\\..\\b\\abs_084unuus3z\\croot\\dill_1692271268687\\work"}
distributed = {file = "..\\..\\..\\..\\..\\..\\b\\abs_5eren88ku4\\croot\\distributed_1701398076011\\work"}
distro = {file = "..\\..\\..\\..\\..\\..\\b\\abs_a3uni_yez3\\croot\\distro_1701455052240\\work"}
dnspython = "==2.6.1"
docopt = "==0.6.2"
docstring-to-markdown = {file = "..\\..\\..\\..\\..\\..\\ci_311\\docstring-to-markdown_1677742566583\\work"}
docutils = {file = "..\\..\\..\\..\\..\\..\\ci_311\\docutils_1676428078664\\work"}
easyocr = "==1.7.1"
editdistance = "==0.8.1"
efficientnet = "==1.0.0"
email-validator = "==2.1.1"
entrypoints = {file = "..\\..\\..\\..\\..\\..\\ci_311\\entrypoints_1676423328987\\work"}
essential-generators = "==1.0"
et-xmlfile = "==1.1.0"
executing = {file = "..\\..\\..\\..\\..\\..\\opt\\conda\\conda-bld\\executing_1646925071911\\work"}
fastapi = "==0.111.0"
fastapi-cli = "==0.0.2"
fastjsonschema = {file = "..\\..\\..\\..\\..\\..\\ci_311\\python-fastjsonschema_1679500568724\\work"}
filelock = {file = "..\\..\\..\\..\\..\\..\\b\\abs_f2gie28u58\\croot\\filelock_1700591233643\\work"}
flake8 = {file = "..\\..\\..\\..\\..\\..\\ci_311\\flake8_1678376624746\\work"}
flask = {file = "..\\..\\..\\..\\..\\..\\b\\abs_efc024w7fv\\croot\\flask_1702980041157\\work"}
flatbuffers = "==24.3.25"
fonttools = "==4.25.0"
fpdf = "==1.7.2"
fr-core-news-sm = {file = "https://github.com/explosion/spacy-models/releases/download/fr_core_news_sm-3.7.0/fr_core_news_sm-3.7.0-py3-none-any.whl"}
frozendict = {file = "..\\..\\..\\..\\..\\..\\b\\abs_2alamqss6p\\croot\\frozendict_1713194885124\\work"}
frozenlist = {file = "..\\..\\..\\..\\..\\..\\b\\abs_d8e__s1ys3\\croot\\frozenlist_1698702612014\\work"}
fsspec = {file = "..\\..\\..\\..\\..\\..\\b\\abs_97mpfsesn0\\croot\\fsspec_1701286534629\\work"}
future = {file = "..\\..\\..\\..\\..\\..\\ci_311_rebuilds\\future_1678998246262\\work"}
fuzzywuzzy = {file = "..\\..\\..\\..\\..\\..\\ci_311\\fuzzywuzzy_1676472075056\\work"}
gast = "==0.5.4"
gitdb = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\gitdb_1617117951232\\work"}
gitpython = {file = "..\\..\\..\\..\\..\\..\\b\\abs_e1lwow9h41\\croot\\gitpython_1696937027832\\work"}
gmpy2 = {file = "..\\..\\..\\..\\..\\..\\ci_311\\gmpy2_1677743390134\\work"}
google-auth = "==2.29.0"
google-auth-oauthlib = "==1.2.0"
google-pasta = "==0.2.0"
greenlet = {file = "..\\..\\..\\..\\..\\..\\b\\abs_a6c75ie0bc\\croot\\greenlet_1702060012174\\work"}
grpcio = "==1.62.2"
h11 = "==0.14.0"
h5py = "==3.11.0"
heapdict = {file = "..\\..\\..\\..\\..\\ktietz\\demo\\mc3\\conda-bld\\heapdict_1630598515714\\work"}
holoviews = {file = "..\\..\\..\\..\\..\\..\\b\\abs_704uucojt7\\croot\\holoviews_1707836477070\\work"}
html5lib = "==1.1"
httpcore = "==1.0.5"
httptools = "==0.6.1"
httpx = "==0.27.0"
hvplot = {file = "..\\..\\..\\..\\..\\..\\b\\abs_3627uzd5h0\\croot\\hvplot_1706712443782\\work"}
hyperlink = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\hyperlink_1610130746837\\work"}
idna = "==3.7"
imagecodecs = {file = "..\\..\\..\\..\\..\\..\\b\\abs_e2g5zbs1q0\\croot\\imagecodecs_1695065012000\\work"}
imageio = {file = "..\\..\\..\\..\\..\\..\\b\\abs_aeqerw_nps\\croot\\imageio_1707247365204\\work"}
imagesize = {file = "..\\..\\..\\..\\..\\..\\ci_311\\imagesize_1676431905616\\work"}
imbalanced-learn = {file = "..\\..\\..\\..\\..\\..\\b\\abs_87es3kd5fi\\croot\\imbalanced-learn_1700648276799\\work"}
imgaug = "==0.4.0"
importlib-metadata = {file = "..\\..\\..\\..\\..\\..\\b\\abs_c1egths604\\croot\\importlib_metadata-suite_1704813568388\\work"}
incremental = {file = "..\\..\\..\\..\\..\\..\\croot\\incremental_1708639938299\\work"}
inflection = "==0.5.1"
iniconfig = {file = "..\\..\\..\\..\\..\\..\\home\\linux1\\recipes\\ci\\iniconfig_1610983019677\\work"}
intake = {file = "..\\..\\..\\..\\..\\..\\ci_311_rebuilds\\intake_1678999914269\\work"}
intel-openmp = "==2021.4.0"
intervaltree = {file = "..\\..\\..\\..\\..\\ktietz\\demo\\mc3\\conda-bld\\intervaltree_1630511889664\\work"}
ipykernel = {file = "..\\..\\..\\..\\..\\..\\b\\abs_c2u94kxcy6\\croot\\ipykernel_1705933907920\\work"}
ipython = "==8.12.3"
ipython-genutils = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\ipython_genutils_1606773439826\\work"}
ipywidgets = {file = "..\\..\\..\\..\\..\\..\\croot\\ipywidgets_1701289330913\\work"}
isort = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\isort_1628603791788\\work"}
itemadapter = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\itemadapter_1626442940632\\work"}
itemloaders = {file = "..\\..\\..\\..\\..\\..\\b\\abs_5e3azgv25z\\croot\\itemloaders_1708639993442\\work"}
itsdangerous = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\itsdangerous_1621432558163\\work"}
"jaraco.classes" = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\jaraco.classes_1620983179379\\work"}
jedi = {file = "..\\..\\..\\..\\..\\..\\ci_311\\jedi_1679427407646\\work"}
jellyfish = {file = "..\\..\\..\\..\\..\\..\\b\\abs_50kgvtnrbj\\croot\\jellyfish_1695193564091\\work"}
jinja2 = {file = "..\\..\\..\\..\\..\\..\\b\\abs_f7x5a8op2h\\croot\\jinja2_1706733672594\\work"}
jmespath = {file = "..\\..\\..\\..\\..\\..\\b\\abs_59jpuaows7\\croot\\jmespath_1700144635019\\work"}
joblib = {file = "..\\..\\..\\..\\..\\..\\b\\abs_1anqjntpan\\croot\\joblib_1685113317150\\work"}
json5 = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\json5_1624432770122\\work"}
jsonpatch = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\jsonpatch_1615747632069\\work"}
jsonpointer = "==2.1"
jsonschema = {file = "..\\..\\..\\..\\..\\..\\b\\abs_d1c4sm8drk\\croot\\jsonschema_1699041668863\\work"}
jsonschema-specifications = {file = "..\\..\\..\\..\\..\\..\\b\\abs_0brvm6vryw\\croot\\jsonschema-specifications_1699032417323\\work"}
jupyter = {file = "..\\..\\..\\..\\..\\..\\b\\abs_4e102rc6e5\\croot\\jupyter_1707947170513\\work"}
jupyter-console = {file = "..\\..\\..\\..\\..\\..\\b\\abs_82xaa6i2y4\\croot\\jupyter_console_1680000189372\\work"}
jupyter-events = {file = "..\\..\\..\\..\\..\\..\\b\\abs_17ajfqnlz0\\croot\\jupyter_events_1699282519713\\work"}
jupyter-lsp = {file = "..\\..\\..\\..\\..\\..\\b\\abs_ecle3em9d4\\croot\\jupyter-lsp-meta_1699978291372\\work"}
jupyter-client = {file = "..\\..\\..\\..\\..\\..\\b\\abs_a6h3c8hfdq\\croot\\jupyter_client_1699455939372\\work"}
jupyter-core = {file = "..\\..\\..\\..\\..\\..\\b\\abs_c769pbqg9b\\croot\\jupyter_core_1698937367513\\work"}
jupyter-server = {file = "..\\..\\..\\..\\..\\..\\b\\abs_7esjvdakg9\\croot\\jupyter_server_1699466495151\\work"}
jupyter-server-terminals = {file = "..\\..\\..\\..\\..\\..\\b\\abs_ec0dq4b50j\\croot\\jupyter_server_terminals_1686870763512\\work"}
jupyterlab = {file = "..\\..\\..\\..\\..\\..\\b\\abs_43venm28fu\\croot\\jupyterlab_1706802651134\\work"}
jupyterlab-pygments = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\jupyterlab_pygments_1601490720602\\work"}
jupyterlab-widgets = {file = "..\\..\\..\\..\\..\\..\\b\\abs_adrrqr26no\\croot\\jupyterlab_widgets_1700169018974\\work"}
jupyterlab-server = {file = "..\\..\\..\\..\\..\\..\\b\\abs_e08i7qn9m8\\croot\\jupyterlab_server_1699555481806\\work"}
keras = "==2.15.0"
keras-applications = "==1.0.8"
keras-ocr = "==0.9.3"
keyring = {file = "..\\..\\..\\..\\..\\..\\b\\abs_dbjc7g0dh2\\croot\\keyring_1678999228878\\work"}
kiwisolver = {file = "..\\..\\..\\..\\..\\..\\ci_311\\kiwisolver_1676431979301\\work"}
langcodes = "==3.4.0"
language-data = "==1.2.0"
lazy-object-proxy = {file = "..\\..\\..\\..\\..\\..\\ci_311\\lazy-object-proxy_1676432050939\\work"}
lazy-loader = {file = "..\\..\\..\\..\\..\\..\\b\\abs_3bn4_r4g42\\croot\\lazy_loader_1695850158046\\work"}
lckr-jupyterlab-variableinspector = {file = "..\\..\\..\\..\\..\\..\\b\\abs_b5yb2mprx2\\croot\\jupyterlab-variableinspector_1701096592545\\work"}
libarchive-c = {file = "..\\..\\..\\..\\..\\..\\tmp\\build\\80754af9\\python-libarchive-c_1617780486945\\work"}
libclang = "==18.1.1"
libmambapy = {file = "..\\..\\..\\..\\..\\..\\b\\abs_2euls_1a38\\croot\\mamba-split_1704219444888\\work\\libmambapy"}
linkify-it-py = {file = "..\\..\\..\\..\\..\\..\\ci_311\\linkify-it-py_1676474436187\\work"}
llvmlite = {file = "..\\..\\..\\..\\..\\..\\b\\abs_da15r8vkf8\\croot\\llvmlite_1706910779994\\work"}
lmdb = {file = "..\\..\\..\\..\\..\\..\\b\\abs_556ronuvb2\\croot\\python-lmdb_1682522366268\\work"}
locket = {file = "..\\..\\..\\..\\..\\..\\ci_311\\locket_1676428325082\\work"}
lxml = {file = "..\\..\\..\\..\\..\\..\\b\\abs_9e7tpg2vv9\\croot\\lxml_1695058219431\\work"}
lz4 = {file = "..\\..\\..\\..\\..\\..\\b\\abs_064u6aszy3\\croot\\lz4_1686057967376\\work"}
marisa-trie = "==1.1.0"
markdown = "==3.6"
markdown-it-py = {file = "..\\..\\..\\..\\..\\..\\b\\abs_a5bfngz6fu\\croot\\markdown-it-py_1684279915556\\work"}
markupsafe = "==2.1.5"
matplotlib = {file = "..\\..\\..\\..\\..\\..\\b\\abs_e26vnvd5s1\\croot\\matplotlib-suite_1698692153288\\work"}
matplotlib-inline = {file = "..\\..\\..\\..\\..\\..\\ci_311\\matplotlib-inline_1676425798036\\work"}
mccabe = {file = "..\\..\\..\\..\\..\\..\\opt\\conda\\conda-bld\\mccabe_1644221741721\\work"}
mdit-py-plugins = {file = "..\\..\\..\\..\\..\\..\\ci_311\\mdit-py-plugins_1676481827414\\work"}
mdurl = {file = "..\\..\\..\\..\\..\\..\\ci_311\\mdurl_1676442676678\\work"}
menuinst = {file = "..\\..\\..\\..\\..\\..\\b\\abs_099kybla52\\croot\\menuinst_1706732987063\\work"}
mistune = {file = "..\\..\\..\\..\\..\\..\\ci_311\\mistune_1676425111783\\work"}
mkl = "==2021.4.0"
mkl-fft = {file = "..\\..\\..\\..\\..\\..\\b\\abs_19i1y8ykas\\croot\\mkl_fft_1695058226480\\work"}
mkl-random = {file = "..\\..\\..\\..\\..\\..\\b\\abs_edwkj1_o69\\croot\\mkl_random_1695059866750\\work"}
mkl-service = "==2.4.0"
ml-dtypes = "==0.3.2"
more-itertools = {file = "..\\..\\..\\..\\..\\..\\b\\abs_36p38zj5jx\\croot\\more-itertools_1700662194485\\work"}
mpmath = {file = "..\\..\\..\\..\\..\\..\\b\\abs_7833jrbiox\\croot\\mpmath_1690848321154\\work"}
msgpack = {file = "..\\..\\..\\..\\..\\..\\ci_311\\msgpack-python_1676427482892\\work"}
multidict = {file = "..\\..\\..\\..\\..\\..\\b\\abs_44ido987fv\\croot\\multidict_1701097803486\\work"}
multipledispatch = {file = "..\\..\\..\\..\\..\\..\\ci_311\\multipledispatch_1676442767760\\work"}
munkres = "==1.1.4"
murmurhash = "==1.0.10"
mypy = {file = "..\\..\\..\\..\\..\\..\\b\\abs_3880czibje\\croot\\mypy-split_1708366584048\\work"}
mypy-extensions = {file = "..\\..\\..\\..\\..\\..\\b\\abs_8f7xiidjya\\croot\\mypy_extensions_1695131051147\\work"}
namex = "==0.0.8"
navigator-updater = {file = "..\\..\\..\\..\\..\\..\\b\\abs_895otdwmo9\\croot\\navigator-updater_1695210220239\\work"}
nbclient = {file = "..\\..\\..\\..\\..\\..\\b\\abs_cal0q5fyju\\croot\\nbclient_1698934263135\\work"}
nbconvert = "==7.16.4"
nbformat = {file = "..\\..\\..\\..\\..\\..\\b\\abs_5a2nea1iu2\\croot\\nbformat_1694616866197\\work"}
nest-asyncio = {file = "..\\..\\..\\..\\..\\..\\b\\abs_65d6lblmoi\\croot\\nest-asyncio_1708532721305\\work"}
networkx = {file = "..\\..\\..\\..\\..\\..\\b\\abs_e6gi1go5op\\croot\\networkx_1690562046966\\work"}
ninja = "==********"
nltk = {file = "..\\..\\..\\..\\..\\..\\b\\abs_a638z6l1z0\\croot\\nltk_1688114186909\\work"}
notebook = {file = "..\\..\\..\\..\\..\\..\\b\\abs_65xjlnf9q4\\croot\\notebook_1708029957105\\work"}

[dev-packages]

[requires]
python_version = "3.12"
