import traceback
from pathlib import Path
from src.app.app import process_model_image_with_module, advanced_mindee_process_ocr
from src.app.utils import constants
from app.camscanner_lite import magic_pro_filter
import logging
import asyncio


# # Set the execution mode
# os.environ['EXEC_MODE'] = 'script'

# Configure logging
# logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')
# Configure root logger to only show ERROR and above
logging.basicConfig(
    level=logging.INFO,  # Set the logging level
    format='%(asctime)s - %(levelname)s - %(message)s',
    force=True, # Add this line to enforce the configuration
)

async def main():
    try:
        path = Path(__file__).parents[0] / "camscanner_lite"

        """
        cropped_model01.jpeg ==> GPM
        cropped_model02.jpeg ==> COOPER_PHARMA_CASA
        cropped_model03.jpeg ==> SOPHADIMS
        cropped_sophaca_iphn.jpg ==> SOPHACA
        cropped_spr_iphn.jpg ==> SPR    
        """

        # input_image_path__f = str(path / 'images/cropped_model01.jpeg') # cropped_model05.jpeg  // cropped_sophaca_iphn.jpg
        # output_image_path__f = str(path / 'output/filtered_img.jpg')
        # magic_pro_filter.apply_magic_pro_filter(input_image_path__f, output_image_path__f, 'GPM')

        # Define the model-image pairs
        model_image_pairs = [
            # (constants.SPR, "../data/origin_BL/cropped_spr_not_cropped_correctly.jpg"),
            # (constants.GPM, "../data/origin_BL/BLS_1.jpg"),
            # (constants.GPM, "../data/origin_BL/old_gpm_cropped_filtered.jpg"),
            # (constants.GPM, "../data/origin_BL/new_gpm__corrected.jpg"),
            # (constants.GPM, "../data/origin_BL/new_gpm_cs.JPG"),
            # (constants.SOPHADIMS, "../data/origin_BL/BLS_3.jpg"),
            # (constants.COOPER_PHARMA_CASA, "../data/origin_BL/BLS_4.jpg"),
            # (constants.COOPER_PHARMA_CASA, "../data/origin_BL/new_copped_cropped_filtered.jpg"),
            # (constants.SPR, "../data/origin_BL/BLS_10.jpg"),
            # (constants.SOPHACA, "../data/origin_BL/BL_scanned_1_1_cs.jpg"),
            # (constants.SOPHACA, "../data/origin_BL/BL_scanned_1_1.jpg"),
            (constants.GPM, "../data/origin_BL/origin_BL/cropped_GPM_25122024.jpg"),
            # (constants.GPM, "../data/origin_BL/origin_BL/gpmScanned.jpg"),
            # (constants.SOPHADIMS, "../data/origin_BL/origin_BL/cropped_SOPHADIM_25122024.jpg"),
            # (constants.COOPER_PHARMA_CASA, "../data/origin_BL/origin_BL/cropped_COOPER_25122024.jpg"),
            # (constants.COOPER_PHARMA_CASA, "../data/origin_BL/origin_BL/cooper_bl_paper.jpg"),
            # (constants.SPR, "../data/origin_BL/origin_BL/BLS_10.jpg"),
            # (constants.COOPER_PHARMA_CASA, "../data/origin_BL/origin_BL/copperAPI3.jpg"),
            # (constants.COOPER_PHARMA_CASA, "../data/origin_BL/origin_BL/toto2.jpg"),
            # (constants.SOPHACA, "../data/captured/sophaca2.jpg"),
            # (constants.SPR, "../data/captured/spr.jpg"),
            # (constants.SOPHADIMS, "../data/captured/sophadims13022025.jpg"),

        ]

        # Define the trained modules
        trained_modules = [
            # 'earthMomma1',
            # 'earthMomma2_18k',
            # 'earthMomma_5k_intr',
            # 'ninetyseven1',
            # 'ninetyseven2',
            # 'ninetysevenlite',
            # 'earthmomma_lite',
            'default'
        ]

        output_dir_path = Path("../data/output_preprocessing")
        output_dir_txt_after_corr_path = Path("../output_after_corr_txt")
        output_dir_txt_before_corr_path = Path("../output_before_corr_txt")

        mode = 'standard' # 'standard' 'mindee_advanced'


        if (mode == 'mindee_advanced'):
            for current_index_first_loop, (model, image_path) in enumerate(model_image_pairs):

                images_url_path = {
                    "origin": image_path,
                    "cropped": None,
                    "filtered": None
                }

                await advanced_mindee_process_ocr(image_path=image_path, model=model, images_url_path=images_url_path,
                                                  isAPI=False)
        else:


            first_run = True
            last_run = False
            # Process each model-image pair with each trained module
            for current_index_first_loop, (model, image_path) in enumerate(model_image_pairs):

                if first_run:
                    for module_name in trained_modules:
                        output_file_path_after_corr = output_dir_txt_after_corr_path / f"{module_name}_corrected_ocr.txt"
                        output_file_path_before_corr = output_dir_txt_before_corr_path / f"{module_name}_tesseract_ocr.txt"

                        # Construct URLs for the images
                        images_url_path = {
                            "origin": None,
                            "cropped": None,
                            "filtered": None
                        }

                        # After correction
                        # if output_file_path_after_corr.exists():
                        #     output_file_path_after_corr.unlink()

                        # # Before correction
                        # if output_file_path_before_corr.exists():
                        #     output_file_path_before_corr.unlink()

                    first_run = False

                    if first_run and output_file_path_after_corr.exists():
                        output_file_path_after_corr.unlink()  # Delete the file if it exists

                for current_index_second_loop, module in enumerate(trained_modules):
                    logging.info(f"Processing model: {model} with image: {image_path} using module: {module}")
                    if len(model_image_pairs) - 1 == current_index_first_loop and len(
                            trained_modules) - 1 == current_index_second_loop:
                        last_run = True
                    await process_model_image_with_module(model, image_path, output_dir_path, module, last_run, False, None,
                                                        None, 0, 0, None, None)
                    # await advanced_mindee_process_ocr(
                    #     image_path=image_path,
                    #     model=model,
                    #     images_url_path=images_url_path,
                    #     isAPI=False
                    # )

            """ --------------------------------------Single Models Start ----------------------------------------------- """

            # model = constants.GPM  # 'SOPHACA' 'SPR' 'SOPHADIMS' 'GPM'  'COOPER_PHARMA_CASA' 'RECAMED' 'GLOBAL'
            # image_path = "../data/origin_BL/BL_scanned_1_1_cs.jpg"
            # image_header_path = '../data/output_preprocessing/BL_header_cropped_processed.jpg'
            # image_footer_path = '../data/output_preprocessing/BL_footer_cropped_processed.jpg'
            # image_table_path = '../data/output_preprocessing/BL_table_final_format.jpg'
            #
            # process_model_image_with_module(model, image_path, output_dir_path, 'default', False)

    except (IOError, ValueError, AttributeError) as e:
        logging.error(f"Error in __main__: {traceback.format_exc()}")


if __name__ == "__main__":
    asyncio.run(main())
