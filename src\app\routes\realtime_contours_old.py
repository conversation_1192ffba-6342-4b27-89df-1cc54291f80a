from fastapi import APIRouter, WebSocket, WebSocketDisconnect
import cv2
import numpy as np
import base64
from starlette.websockets import WebSocketState
from typing import Dict
import logging
import json

router = APIRouter()

class RealtimeContoursManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        connection_id = str(id(websocket))
        self.active_connections[connection_id] = websocket
        logging.info(f"WebSocket connection established with id: {connection_id}")
        return connection_id

    def disconnect(self, connection_id: str):
        if connection_id in self.active_connections:
            websocket = self.active_connections.pop(connection_id)
            logging.info(f"WebSocket connection closed with id: {connection_id}")

manager = RealtimeContoursManager()

def assess_image_quality(img):
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    blur = cv2.Laplacian(gray, cv2.CV_64F).var()
    brightness = np.mean(gray)
    contrast = np.std(gray)

    quality = "good"
    if blur < 100:
        quality = "blurry"
    elif brightness < 50 or brightness > 200:
        quality = "poor_brightness"
    elif contrast < 30:
        quality = "low_contrast"

    return quality


@router.websocket("/websocket/realtime_processing")
async def realtime_contours(websocket: WebSocket):
    connection_id = await manager.connect(websocket)
    if connection_id is None:
        return
    try:
        while True:
            logging.info("Waiting for frame data...")
            data = await websocket.receive_text()
            logging.info(f"Received frame data: {data[:100]}...")  # Log only the first 100 characters

            try:
                frame_data = json.loads(data)  # Parse JSON data
                logging.info(f"Parsed JSON data: {frame_data}")  # Log the parsed JSON data

                if frame_data.get('type') == 'ping':
                    continue  # Skip processing for ping messages

                if 'image' not in frame_data:
                    raise ValueError("Missing 'image' key in received data")

                img_data = base64.b64decode(frame_data['image'].split(',')[1])
                nparr = np.frombuffer(img_data, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                logging.info(f"Decoded image shape: {img.shape}")

                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                edges = cv2.Canny(gray, 50, 150, apertureSize=3)
                contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                logging.info(f"Found {len(contours)} contours")

                # Filter contours based on area and aspect ratio
                document_contours = []
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area < 5000 or area > 500000:
                        continue  # Skip small and large contours

                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / float(h)
                    if 0.5 < aspect_ratio < 2.0:  # Adjust the aspect ratio range as needed
                        document_contours.append(contour)

                if document_contours:
                    largest_contour = max(document_contours, key=cv2.contourArea)
                    epsilon = 0.02 * cv2.arcLength(largest_contour, True)
                    approx = cv2.approxPolyDP(largest_contour, epsilon, True)

                    if len(approx) == 4:
                        box = approx.reshape(4, 2)
                    else:
                        rect = cv2.minAreaRect(largest_contour)
                        box = cv2.boxPoints(rect)

                    box = np.int0(box)
                    quality = assess_image_quality(img)

                    response = {
                        "contour": box.tolist(),
                        "quality": quality
                    }
                    logging.info(f"Sending response: {response}")
                    await websocket.send_text(json.dumps(response))
                else:
                    logging.warning("No document contour found")
                    await websocket.send_text(json.dumps({"error": "No document contour found"}))
            except json.JSONDecodeError as e:
                logging.error(f"JSON decode error: {e}")
                await websocket.send_text(json.dumps({"error": "Invalid JSON format"}))
            except KeyError as e:
                logging.error(f"Key error: {e}")
                await websocket.send_text(json.dumps({"error": f"Missing key: {e}"}))
            except ValueError as e:
                logging.error(f"Value error: {e}")
                await websocket.send_text(json.dumps({"error": str(e)}))
            except Exception as e:
                logging.error(f"Error processing frame: {str(e)}")
                await websocket.send_text(json.dumps({"error": str(e)}))
    except Exception as e:
        logging.error(f"Unexpected error in WebSocket: {e}")
    finally:
        manager.disconnect(connection_id)
