import cv2
import numpy as np
from src.app.utils import constants
import logging
import traceback

# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def apply_color_palette(image, palette_settings):
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)

    # Apply hue shift
    h = (h.astype(int) + palette_settings.get('hue_shift', 0)) % 180
    h = h.astype(np.uint8)

    # Apply saturation factor
    s = cv2.multiply(s, palette_settings.get('saturation_factor', 1.0))

    # Merge the channels back and convert to BGR
    hsv_colored = cv2.merge([h, s, v])
    image = cv2.cvtColor(hsv_colored, cv2.COLOR_HSV2BGR)

    # Apply brightness adjustment
    image = cv2.convertScaleAbs(image, alpha=1.0, beta=palette_settings.get('brightness_beta', 0))

    # Apply white intensity adjustment
    white_intensity = palette_settings.get('white_intensity', 1.0)
    white_mask = cv2.inRange(image, np.array([200, 200, 200]), np.array([255, 255, 255]))
    if white_intensity != 1.0:
        image[white_mask != 0] = cv2.convertScaleAbs(image[white_mask != 0], alpha=white_intensity, beta=0)

    # Apply black intensity adjustment
    black_intensity = palette_settings.get('black_intensity', 1.0)
    black_mask = cv2.inRange(image, np.array([0, 0, 0]), np.array([50, 50, 50]))
    if black_intensity != 1.0:
        image[black_mask != 0] = cv2.convertScaleAbs(image[black_mask != 0], alpha=black_intensity, beta=0)

    return image


def detect_table_contour(img):
    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Apply Gaussian blur to reduce noise
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # Detect edges
    edges = cv2.Canny(blurred, 50, 150, apertureSize=3)

    # Find lines using Hough Transform
    lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, minLineLength=100, maxLineGap=10)

    if lines is None:
        return 0  # No lines detected, return 0 angle

    # Separate horizontal and vertical lines
    horizontal_lines = []
    vertical_lines = []
    for line in lines:
        x1, y1, x2, y2 = line[0]
        if abs(x2 - x1) > abs(y2 - y1):
            horizontal_lines.append(line)
        else:
            vertical_lines.append(line)

    if not horizontal_lines or not vertical_lines:
        return 0  # Not enough lines to form a table, return 0 angle

    # Find the average angle of horizontal lines
    angles = []
    for line in horizontal_lines:
        x1, y1, x2, y2 = line[0]
        angle = np.arctan2(y2 - y1, x2 - x1)
        angles.append(angle)

    avg_angle = np.mean(angles)
    angle_deg = np.degrees(avg_angle)

    # Draw the detected lines
    for line in horizontal_lines + vertical_lines:
        x1, y1, x2, y2 = line[0]
        cv2.line(img, (x1, y1), (x2, y2), (0, 255, 0), 2)

    return angle_deg


# rotate the image with given theta value
def rotate(img, angle):
    (h, w) = img.shape[:2]
    center = (w // 2, h // 2)
    M = cv2.getRotationMatrix2D(center, angle, 1.0)
    rotated = cv2.warpAffine(img, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)
    return rotated


def adjust_whiteness(image, percentile_value=97.5):
    """
    Adjusts the whiteness of the image using the white balancing method.

    :param image: The input image (BGR format).
    :param percentile_value: Percentile value to normalize the intensity values in the image.
    :return: The adjusted image.
    """
    image = image.astype(np.float32) / 255.0  # Normalize the image

    # Compute the percentile value for each channel
    percentiles = np.percentile(image, percentile_value, axis=(0, 1))

    # Normalize each channel by its corresponding percentile value
    white_balanced = np.clip(image / percentiles, 0, 1)

    # Convert back to 8-bit image
    white_balanced = (white_balanced * 255).astype(np.uint8)

    return white_balanced


def apply_magic_pro_filter(image_path, output_path, model, palette_name='custom'):

    try:
        # settings = constants.model_settings_magicpro_filter[model]
        settings = constants.model_settings_magicpro_filter['GLOBAL']
        logging.info("image_path: %s", image_path)

        image = cv2.imread(image_path)
        if image is None:
            raise FileNotFoundError(f"Fichier image introuvable à l'adresse: {image_path}")

        assert image is not None, "Failed to load image"


        """ Clear Magic Pro Filter  """
        # Apply Gaussian Blur to reduce noise
        blurred = cv2.GaussianBlur(image, settings['gaussian_blur'], 0)

        # Convert to LAB color space to work with brightness
        lab = cv2.cvtColor(blurred, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)

        # Apply CLAHE to the L-channel (brightness)
        clahe = cv2.createCLAHE(clipLimit=settings['clahe_clip_limit'], tileGridSize=settings['clahe_tile_grid_size'])
        cl = clahe.apply(l)

        # Merge the CLAHE enhanced L-channel with the a and b channels
        limg = cv2.merge((cl, a, b))

        # Convert back to BGR color space
        enhanced_image = cv2.cvtColor(limg, cv2.COLOR_LAB2BGR)

        # Apply noise reduction
        denoised_image = cv2.fastNlMeansDenoisingColored(enhanced_image, None, settings['h'],
                                                         settings['hForColorComponents'], settings['templateWindowSize'],
                                                         settings['searchWindowSize'])

        # Increase contrast and brightness
        """ Clear the white fog or beam effect in the image """

        """ Increase contrast and brightness (lighting) """
        # Increase contrast and brightness
        adjusted = cv2.convertScaleAbs(denoised_image, alpha=settings['contrast_alpha'], beta=settings['brightness_beta'])

        # Apply sharpening kernel
        kernel = np.array([[0, -1, 0],
                           [-1, 5, -1],
                           [0, -1, 0]])
        sharpened = cv2.filter2D(adjusted, -1, kernel)

        """ Create Mask of the Thickened text """
        # Convert to grayscale
        gray = cv2.cvtColor(sharpened, cv2.COLOR_BGR2GRAY)

        # Apply adaptive thresholding to get binary image
        binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY,
                                       settings['adaptive_thresh_block_size'], settings['adaptive_thresh_C'])

        # Use morphological operations to thicken the text
        kernel = np.ones(settings['dilate_kernel_size'], np.uint8)
        thickened = cv2.dilate(binary, kernel, iterations=settings['dilate_iterations'])

        """ Apply the Mask of the Thickened text """
        # Merge thickened text back with the original color image
        colored_thickened = cv2.bitwise_and(sharpened, sharpened, mask=thickened)

        # Convert to HSV color space
        hsv = cv2.cvtColor(colored_thickened, cv2.COLOR_BGR2HSV)
        h, s, v = cv2.split(hsv)

        # Increase saturation
        s = cv2.multiply(s, settings['saturation_factor'])

        # Merge the channels back and convert to BGR
        hsv_colored = cv2.merge([h, s, v])
        final_image = cv2.cvtColor(hsv_colored, cv2.COLOR_HSV2BGR)

        """-----------thicken the font-----------"""

        # Apply additional filter to thicken the font further
        gray_final = cv2.cvtColor(final_image, cv2.COLOR_BGR2GRAY)
        binary_final = cv2.adaptiveThreshold(gray_final, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY,
                                             settings['adaptive_thresh_block_size_2'], settings['adaptive_thresh_C_2'])
        kernel_final = np.ones(settings['dilate_kernel_size_2'], np.uint8)
        thickened_final = cv2.dilate(binary_final, kernel_final, iterations=settings['dilate_iterations_2'])

        final_image_colored_thickened = cv2.bitwise_and(final_image, final_image, mask=thickened_final)

        """------------black_point----------"""
        # Convert to LAB color space
        lab = cv2.cvtColor(final_image_colored_thickened, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)

        # Apply Black Point adjustment
        black_point = settings['black_point']
        l = cv2.addWeighted(l, 1.0, l, 0, -black_point)

        lab = cv2.merge((l, a, b))
        final_image_colored_thickened = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)

        """ -----------color picker----------- """
        # Apply color palette if specified
        if palette_name and palette_name in settings['color_palettes']:
            palette_settings = settings['color_palettes'][palette_name]
            final_image_colored_thickened = apply_color_palette(final_image_colored_thickened, palette_settings)

        """ -----------whiteness adjustment----------- """
        if settings['whiteness_adjustment']:
            final_image_colored_thickened = adjust_whiteness(final_image_colored_thickened,
                                                             settings['percentile_slider'])

        # """ -----------correct skew----------- """
        # original = final_image_colored_thickened.copy()
        # angle = detect_table_contour(final_image_colored_thickened)
        # final_image_colored_thickened = rotate(original, angle)

        """ Save the image transformed """
        # Save the result
        cv2.imwrite(output_path, final_image_colored_thickened)
        logging.info(f"Filtered image saved to {output_path}")

        return output_path, final_image_colored_thickened

    except FileNotFoundError as fnf_error:
        logging.error(f"Erreur de fichier: {fnf_error}")
        raise fnf_error
    except Exception as e:
        logging.error(f"Une erreur est survenue dans la méthode apply_magic_pro_filter: {traceback.format_exc()}")
        raise e


# if __name__ == "__main__":
#     # input_image_path = 'output/BL_scanned_1_1.jpg'  # cropped_model05.jpeg
#     input_image_path = 'C:\\Users\\<USER>\\Downloads\\Work __Abderrahmane_ouhna\\OCR_DOCUMENT_GROSSISTE\\ocr_document_grossiste\\temp\\smart_crop_output\\cropped_model06.jpeg'  # cropped_model05.jpeg
#     output_image_path = 'output/filtered_image.jpg'
#     apply_magic_pro_filter(input_image_path, output_image_path, 'GPM')
