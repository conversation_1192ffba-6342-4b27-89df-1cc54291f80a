import numpy as np
import cv2
from matplotlib import pyplot as plt
import os
import pytesseract as tess
from pathlib import Path
from PIL import Image, ImageDraw
from src import constants
from src.config import TESSERACT_PATH
import traceback
import logging
import json
from datetime import datetime
from src import constants
from collections import OrderedDict
from src.document_model import DocumentModel
from fuzzywuzzy import process, fuzz
from src.helpers import *
import logging
import traceback
from src.camscanner_lite import smart_crop, magic_pro_filter
from src import constants, pre_processing
import numpy as np
import os
from src.ocr import header
import traceback
import logging
import cv2
from docx.shared import Pt
from docx import Document
from fpdf import FPDF
import os
from pathlib import Path
from fuzzywuzzy import process, fuzz
from difflib import SequenceMatcher
from src import constants
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
import re
import json
import json
import re
from datetime import datetime
import cv2
import os
import sys
from pathlib import Path
from dotenv import load_dotenv
import re
import os
import time
from pathlib import Path
import logging
import uuid
from datetime import datetime
import json
from pathlib import Path
import logging
import traceback
from src.ocr import table, header, footer
from src import constants, models_utils, pre_processing
from src.document_model import DocumentModel
from enum import Enum
import uvicorn
from fastapi import FastAPI, File, UploadFile, Form, HTTPException, Request
from fastapi.responses import JSONResponse
from pathlib import Path
from src import pre_processing, identify_supplier
from src.app import process_model_image_with_module
from src.camscanner_lite import smart_crop, magic_pro_filter
import os
import uuid
from fastapi.responses import FileResponse
from typing import List, Optional
import aiofiles
import logging
import traceback
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from src.config import API_URL, ENVIRONMENT  # Import ENVIRONMENT from config
import requests
from io import BytesIO
from PIL import Image
from pydantic import BaseModel
import cv2
from datetime import datetime
import asyncio
from concurrent.futures import ThreadPoolExecutor
import zipfile
import fitz
from src.camscanner_lite import smart_crop, magic_pro_filter
import logging
import traceback
from pathlib import Path
from src import constants, app
from fuzzywuzzy import process, fuzz
from src import constants
from src import models_utils
from src.helpers import *
import logging
import traceback
from PIL import Image, ImageDraw
from pytesseract import image_to_string
import re
import json
from src.document_model import DocumentModel
from src import constants
from src.helpers import *
from src.config import SYS_ARGV
import traceback
import logging
import cv2
import pytesseract
from PIL import Image
import re
from pathlib import Path
import logging
from src.document_model import DocumentModel
from src import constants, models_utils
from src.helpers import *
from src.config import SYS_ARGV
from pathlib import Path
import cv2
import pytesseract
from PIL import Image
import os
from pathlib import Path
import logging
import traceback
from fastapi import HTTPException
from src.camscanner_lite import polygon_interacter as poly_i
import numpy as np
import matplotlib.pyplot as plt
import itertools
import math
import cv2
from src.camscanner_lite.pyimagesearch import transform
from src.camscanner_lite.pyimagesearch import imutils
from scipy.spatial import distance as dist
from matplotlib.patches import Polygon
import numpy as np
from matplotlib.lines import Line2D
from matplotlib.artist import Artist
import cv2
import numpy as np
from src import constants
import logging
import traceback
import cv2
import numpy as np
import os
from scipy.spatial import distance as dist
from scipy.spatial import distance as dist
import numpy as np
import cv2
import numpy as np
import cv2