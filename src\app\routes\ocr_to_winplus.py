from fastapi import APIRouter, HTTPException, WebSocketDisconnect, Depends, Header, Body
from src.app.dependencies import get_current_user, check_headers, check_tenant_user_headers_winplus
import logging
import json
from ..config import API_URL, WINPLUS_URL
from ..utils.document_model import DocumentModel
from ..utils.helpers import get_temp_path, write_image_async, get_base_name, get_supplier_id
from ..utils.models import List, ImageData, PreBlOcrResponse, UpdateStatusRequest
from ..utils.db_operations import save_response_to_db, get_all_pre_bl_ocr, get_pre_bl_ocr_by_id, update_bl_status, update_bl_status_valider, get_pre_bl_ocr_by_random_id
from concurrent.futures import ThreadPoolExecutor
import asyncio
import traceback

router = APIRouter()

logger = logging.getLogger(__name__)

# Define the URLs
apiUrl = API_URL
winPlusUrl = WINPLUS_URL

# Initialize a ThreadPoolExecutor
executor = ThreadPoolExecutor(max_workers=10)


@router.get("/getAllBl")
async def get_pre_bl_ocr(auth_data: dict = Depends(check_tenant_user_headers_winplus)):
    try:
        # Run `process_identify_supplier` in a ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            lambda: asyncio.run(get_pre_bl_ocr_process(auth_data))
        )

        # Return the result
        return result

    except HTTPException as http_exc:
        logging.error(f"Erreur HTTP: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logging.error(f"Erreur inattendue: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")


@router.get("/getBlById/{id_bl}", response_model=PreBlOcrResponse)
async def get_pre_bl_ocr_by_id_endpoint(id_bl: int, auth_data: dict = Depends(check_tenant_user_headers_winplus)):
    try:
        # Run `process_identify_supplier` in a ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            lambda: asyncio.run(get_pre_bl_ocr_by_id_process(id_bl, auth_data))
        )

        # Return the result
        return result

    except HTTPException as http_exc:
        logging.error(f"Erreur HTTP: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logging.error(f"Erreur inattendue: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")


@router.get("/getBlByRandomId/{random_id}", response_model=PreBlOcrResponse)
async def get_pre_bl_ocr_by_random_endpoint(random_id: str, auth_data: dict = Depends(check_tenant_user_headers_winplus)):
    try:
        # Run `process_identify_supplier` in a ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            lambda: asyncio.run(get_pre_bl_ocr_by_random_process(random_id, auth_data))
        )

        # Return the result
        return result

    except HTTPException as http_exc:
        logging.error(f"Erreur HTTP: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logging.error(f"Erreur inattendue: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")


@router.put("/updateStatus/{bl_id}", dependencies=[Depends(get_current_user), Depends(check_headers)])
async def update_bl_status_endpoint(bl_id: int, request: UpdateStatusRequest):
    try:
        # Run `process_identify_supplier` in a ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            lambda: asyncio.run(update_bl_status_process(bl_id, request))
        )

        # Return the result
        return result

    except HTTPException as http_exc:
        logging.error(f"Erreur HTTP: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logging.error(f"Erreur inattendue: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")


@router.put("/updateBlStatusValider/{bl_id}")
async def update_bl_status_valider_endpoint(bl_id: int, auth_data: dict = Depends(check_tenant_user_headers_winplus)):
    try:
        # Run `process_identify_supplier` in a ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            lambda: asyncio.run(update_bl_status_valider_process(bl_id, auth_data))
        )

        # Return the result
        return result

    except HTTPException as http_exc:
        logging.error(f"Erreur HTTP: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logging.error(f"Erreur inattendue: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")



async def get_pre_bl_ocr_process(auth_data: dict = Depends(check_tenant_user_headers_winplus)):
    try:
        user_id = auth_data["user"].get("sub")
        tenant_id = auth_data["tenant"].get("sub")

        logger.info(f"User ID: {user_id}")
        logger.info(f"Tenant ID: {tenant_id}")

        if not user_id or not tenant_id:
            raise HTTPException(status_code=401, detail="Invalid user or tenant ID")

        results = get_all_pre_bl_ocr(user_id, tenant_id)
        return results
    except Exception as e:
        logger.error(f"Error in get_pre_bl_ocr: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


async def get_pre_bl_ocr_by_id_process(id_bl: int, auth_data: dict = Depends(check_tenant_user_headers_winplus)):
    try:
        user_id = auth_data["user"].get("sub")
        tenant_id = auth_data["tenant"].get("sub")

        if not user_id or not tenant_id:
            raise HTTPException(status_code=401, detail="Invalid user or tenant ID")

        result = get_pre_bl_ocr_by_id(id_bl, user_id, tenant_id)
        if result is None:
            raise HTTPException(status_code=404, detail=f"Pre BL OCR with ID {id_bl} not found")
        return PreBlOcrResponse(**result)
    except Exception as e:
        logger.error(f"Error in get_pre_bl_ocr_by_id_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


async def get_pre_bl_ocr_by_random_process(random_id: str, auth_data: dict = Depends(check_tenant_user_headers_winplus)):
    try:
        user_id = auth_data["user"].get("sub")
        tenant_id = auth_data["tenant"].get("sub")

        if not user_id or not tenant_id:
            raise HTTPException(status_code=401, detail="Invalid user or tenant ID")

        result = get_pre_bl_ocr_by_random_id(random_id, user_id, tenant_id)
        if result is None:
            raise HTTPException(status_code=404, detail=f"Pre BL OCR with ID {random_id} not found")
        return PreBlOcrResponse(**result)
    except Exception as e:
        logger.error(f"Error in get_pre_bl_ocr_by_id_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


async def update_bl_status_process(bl_id: int, request: UpdateStatusRequest):
    try:
        # Retrieve the supplier ID based on the supplier name from the request
        supplier_id = get_supplier_id(request.supplier_name)
        updated = update_bl_status(bl_id, request.status, request.id_BL_origine, request.date_BL_origine,
                                   request.supplier_name, supplier_id)
        if updated:
            return {"message": f"Status updated successfully for BL ID {bl_id}"}
        else:
            raise HTTPException(status_code=404, detail=f"BL with ID {bl_id} not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def update_bl_status_valider_process(bl_id: int, auth_data: dict = Depends(check_tenant_user_headers_winplus)):
    try:
        user_id = auth_data["user"].get("sub")
        tenant_id = auth_data["tenant"].get("sub")

        if not user_id or not tenant_id:
            raise HTTPException(status_code=401, detail="Invalid user or tenant ID")

        updated = update_bl_status_valider(bl_id, user_id, tenant_id)
        if updated:
            return {"message": f"Status updated successfully for BL ID {bl_id}"}
        else:
            raise HTTPException(status_code=404, detail=f"BL with ID {bl_id} not found")
    except Exception as e:
        logger.error(f"Error in update_bl_status_valider_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
